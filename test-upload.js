const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testUpload() {
  try {
    console.log('Testing CSV upload...');
    
    // Create form data
    const form = new FormData();
    form.append('csvFile', fs.createReadStream('Sponsored_Products_adgroup_search_terms_Jan_18_2025.csv'));
    
    // Upload the file
    const uploadResponse = await axios.post('http://localhost:3000/api/upload', form, {
      headers: {
        ...form.getHeaders(),
      },
    });
    
    console.log('Upload successful!');
    console.log('Job ID:', uploadResponse.data.jobId);
    console.log('Status:', uploadResponse.data.status);
    
    const jobId = uploadResponse.data.jobId;
    
    // Poll for completion
    console.log('\nPolling for job completion...');
    let completed = false;
    let attempts = 0;
    const maxAttempts = 30; // 5 minutes max
    
    while (!completed && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
      
      try {
        const statusResponse = await axios.get(`http://localhost:3000/api/analysis/${jobId}`);
        const status = statusResponse.data.status;
        const progress = statusResponse.data.progress;
        
        console.log(`Attempt ${attempts + 1}: Status = ${status}, Progress = ${progress}%`);
        
        if (status === 'completed') {
          completed = true;
          console.log('\n✅ CSV processing completed!');
          
          // Show some results
          if (statusResponse.data.analysis) {
            console.log('\n📊 Analysis Results:');
            const metrics = statusResponse.data.analysis.aggregatedMetrics;
            if (metrics) {
              console.log(`- Total Keywords: ${metrics.total_rows}`);
              console.log(`- Total Impressions: ${metrics.total_impressions}`);
              console.log(`- Total Clicks: ${metrics.total_clicks}`);
              console.log(`- Total Spend: $${metrics.total_spend}`);
              console.log(`- Total Sales: $${metrics.total_sales}`);
              console.log(`- Overall ROAS: ${metrics.calculated_roas?.toFixed(2)}`);
              console.log(`- Overall ACOS: ${metrics.calculated_acos?.toFixed(2)}%`);
            }
          }
          
        } else if (status === 'failed') {
          console.log('\n❌ Job failed:', statusResponse.data.error?.message);
          break;
        }
        
        attempts++;
      } catch (error) {
        console.log(`Error checking status: ${error.message}`);
        attempts++;
      }
    }
    
    if (!completed && attempts >= maxAttempts) {
      console.log('\n⏰ Timeout waiting for job completion');
    }
    
  } catch (error) {
    console.error('Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testUpload();
