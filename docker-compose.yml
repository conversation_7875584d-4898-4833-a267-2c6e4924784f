version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: ad_analysis_postgres
    environment:
      POSTGRES_DB: ad_analysis_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./src/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ad_analysis_network

  redis:
    image: redis:7-alpine
    container_name: ad_analysis_redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - ad_analysis_network

  app:
    build: .
    container_name: ad_analysis_app
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ad_analysis_db
      DB_USER: postgres
      DB_PASSWORD: postgres
      REDIS_HOST: redis
      REDIS_PORT: 6379
      OPENAI_API_KEY: ${OPENAI_API_KEY}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./uploads:/app/uploads
    networks:
      - ad_analysis_network

volumes:
  postgres_data:
  redis_data:

networks:
  ad_analysis_network:
    driver: bridge
