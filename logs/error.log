{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mCSV Processing worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mCSV Processing worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mCSV Processing worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:01:581"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:01:581"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:01:581"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mCSV Processing worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mCSV Processing worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mCSV Processing worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:22:5822"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:22:5822"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:22:5822"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:33:5833"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:33:5833"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:39:5839"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:39:5839"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:44:5844"}
{"error":"relation \"analysis_jobs\" does not exist","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase query error\u001b[39m","text":"\n        SELECT \n          status,\n          COUNT(*) as count,\n          AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_processing_time\n        FROM analysis_jobs \n        WHERE created_at >= NOW() - INTERVAL '30 days'\n        GROUP BY status\n      ","timestamp":"2025-06-15 19:59:47:5947"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting analysis job statistics relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 19:59:47:5947"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting upload status relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 19:59:47:5947"}
{"error":"relation \"analysis_jobs\" does not exist","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase query error\u001b[39m","text":"\n        SELECT \n          status,\n          COUNT(*) as count,\n          AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_processing_time\n        FROM analysis_jobs \n        WHERE created_at >= NOW() - INTERVAL '30 days'\n        GROUP BY status\n      ","timestamp":"2025-06-15 20:00:32:032"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting analysis job statistics relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 20:00:32:032"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting upload status relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 20:00:32:032"}
{"error":"relation \"analysis_jobs\" does not exist","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase query error\u001b[39m","text":"\n        SELECT \n          status,\n          COUNT(*) as count,\n          AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_processing_time\n        FROM analysis_jobs \n        WHERE created_at >= NOW() - INTERVAL '30 days'\n        GROUP BY status\n      ","timestamp":"2025-06-15 20:00:33:033"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting analysis job statistics relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 20:00:33:033"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting upload status relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 20:00:33:033"}
{"error":"relation \"analysis_jobs\" does not exist","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase query error\u001b[39m","text":"\n        SELECT \n          status,\n          COUNT(*) as count,\n          AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_processing_time\n        FROM analysis_jobs \n        WHERE created_at >= NOW() - INTERVAL '30 days'\n        GROUP BY status\n      ","timestamp":"2025-06-15 20:00:36:036"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting analysis job statistics relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 20:00:36:036"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting upload status relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 20:00:36:036"}
