{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSecurity middleware applied\u001b[39m","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing application...\u001b[39m","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to PostgreSQL database\u001b[39m","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection test successful\u001b[39m","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to Redis\u001b[39m","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis client ready\u001b[39m","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mCSV Processing worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connection established\u001b[39m","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mApplication initialized successfully\u001b[39m","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mCSV Processing worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:57:56:5756"}
{"environment":"development","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000\u001b[39m","port":"3000","timestamp":"2025-06-15 19:57:56:5756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mCSV Processing worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:01:581"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:01:581"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:01:581"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSecurity middleware applied\u001b[39m","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing application...\u001b[39m","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to PostgreSQL database\u001b[39m","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection test successful\u001b[39m","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to Redis\u001b[39m","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis client ready\u001b[39m","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mCSV Processing worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connection established\u001b[39m","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mApplication initialized successfully\u001b[39m","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mCSV Processing worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:17:5817"}
{"environment":"development","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000\u001b[39m","port":"3000","timestamp":"2025-06-15 19:58:17:5817"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mCSV Processing worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:22:5822"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:22:5822"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:22:5822"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSecurity middleware applied\u001b[39m","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing application...\u001b[39m","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to PostgreSQL database\u001b[39m","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection test successful\u001b[39m","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to Redis\u001b[39m","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis client ready\u001b[39m","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connection established\u001b[39m","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mApplication initialized successfully\u001b[39m","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:28:5828"}
{"environment":"development","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000\u001b[39m","port":"3000","timestamp":"2025-06-15 19:58:28:5828"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAnalysis worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:33:5833"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:33:5833"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSecurity middleware applied\u001b[39m","timestamp":"2025-06-15 19:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing application...\u001b[39m","timestamp":"2025-06-15 19:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to PostgreSQL database\u001b[39m","timestamp":"2025-06-15 19:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection test successful\u001b[39m","timestamp":"2025-06-15 19:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to Redis\u001b[39m","timestamp":"2025-06-15 19:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis client ready\u001b[39m","timestamp":"2025-06-15 19:58:39:5839"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connection established\u001b[39m","timestamp":"2025-06-15 19:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mApplication initialized successfully\u001b[39m","timestamp":"2025-06-15 19:58:39:5839"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:39:5839"}
{"environment":"development","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000\u001b[39m","port":"3000","timestamp":"2025-06-15 19:58:39:5839"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mOptimization worker error this._client.defineCommand is not a function\u001b[39m","stack":"TypeError: this._client.defineCommand is not a function\n    at RedisConnection.loadCommands (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:122:30)\n    at RedisConnection.init (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/bullmq/dist/cjs/classes/redis-connection.js:139:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15 19:58:44:5844"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSecurity middleware applied\u001b[39m","timestamp":"2025-06-15 19:58:51:5851"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing application...\u001b[39m","timestamp":"2025-06-15 19:58:51:5851"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to PostgreSQL database\u001b[39m","timestamp":"2025-06-15 19:58:51:5851"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection test successful\u001b[39m","timestamp":"2025-06-15 19:58:51:5851"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to Redis\u001b[39m","timestamp":"2025-06-15 19:58:51:5851"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis client ready\u001b[39m","timestamp":"2025-06-15 19:58:51:5851"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connection established\u001b[39m","timestamp":"2025-06-15 19:58:51:5851"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mApplication initialized successfully\u001b[39m","timestamp":"2025-06-15 19:58:51:5851"}
{"environment":"development","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000\u001b[39m","port":"3000","timestamp":"2025-06-15 19:58:51:5851"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSecurity middleware applied\u001b[39m","timestamp":"2025-06-15 19:59:00:590"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing application...\u001b[39m","timestamp":"2025-06-15 19:59:00:590"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to PostgreSQL database\u001b[39m","timestamp":"2025-06-15 19:59:00:590"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection test successful\u001b[39m","timestamp":"2025-06-15 19:59:00:590"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to Redis\u001b[39m","timestamp":"2025-06-15 19:59:00:590"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis client ready\u001b[39m","timestamp":"2025-06-15 19:59:00:590"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connection established\u001b[39m","timestamp":"2025-06-15 19:59:00:590"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mApplication initialized successfully\u001b[39m","timestamp":"2025-06-15 19:59:00:590"}
{"environment":"development","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000\u001b[39m","port":"3000","timestamp":"2025-06-15 19:59:00:590"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection test successful\u001b[39m","timestamp":"2025-06-15 19:59:26:5926"}
{"duration":"7ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":200,"timestamp":"2025-06-15 19:59:26:5926","url":"/health","userAgent":"curl/8.5.0"}
{"duration":"1ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":200,"timestamp":"2025-06-15 19:59:40:5940","url":"/api","userAgent":"curl/8.5.0"}
{"duration":"22ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":200,"timestamp":"2025-06-15 19:59:47:5947","url":"/","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":200,"timestamp":"2025-06-15 19:59:47:5947","url":"/static/css/main.09d07e9c.css","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":200,"timestamp":"2025-06-15 19:59:47:5947","url":"/static/js/main.115b848b.js","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"relation \"analysis_jobs\" does not exist","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase query error\u001b[39m","text":"\n        SELECT \n          status,\n          COUNT(*) as count,\n          AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_processing_time\n        FROM analysis_jobs \n        WHERE created_at >= NOW() - INTERVAL '30 days'\n        GROUP BY status\n      ","timestamp":"2025-06-15 19:59:47:5947"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting analysis job statistics relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 19:59:47:5947"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting upload status relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 19:59:47:5947"}
{"duration":"6ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":500,"timestamp":"2025-06-15 19:59:47:5947","url":"/status","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":200,"timestamp":"2025-06-15 19:59:47:5947","url":"/favicon.ico","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 19:59:50:5950","url":"/favicon.ico","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 19:59:51:5951","url":"/favicon.ico","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 19:59:54:5954","url":"/favicon.ico","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSecurity middleware applied\u001b[39m","timestamp":"2025-06-15 20:00:05:05"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing application...\u001b[39m","timestamp":"2025-06-15 20:00:05:05"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to PostgreSQL database\u001b[39m","timestamp":"2025-06-15 20:00:05:05"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection test successful\u001b[39m","timestamp":"2025-06-15 20:00:05:05"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to Redis\u001b[39m","timestamp":"2025-06-15 20:00:05:05"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis client ready\u001b[39m","timestamp":"2025-06-15 20:00:05:05"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connection established\u001b[39m","timestamp":"2025-06-15 20:00:05:05"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mApplication initialized successfully\u001b[39m","timestamp":"2025-06-15 20:00:05:05"}
{"environment":"development","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000\u001b[39m","port":"3000","timestamp":"2025-06-15 20:00:05:05"}
{"filename":"csvFile-1749997812155-392122330.csv","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mFile upload received\u001b[39m","mimetype":"text/csv","originalName":"Sponsored_Products_adgroup_search_terms_Jan_18_2025.csv","size":6590,"timestamp":"2025-06-15 20:00:12:012"}
{"duration":"23ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"POST","status":400,"timestamp":"2025-06-15 20:00:12:012","url":"/","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"POST","status":400,"timestamp":"2025-06-15 20:00:16:016","url":"/validate","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"POST","status":400,"timestamp":"2025-06-15 20:00:17:017","url":"/validate","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"POST","status":400,"timestamp":"2025-06-15 20:00:17:017","url":"/validate","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"POST","status":400,"timestamp":"2025-06-15 20:00:17:017","url":"/validate","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"filename":"csvFile-1749997818925-108896744.csv","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mFile upload received\u001b[39m","mimetype":"text/csv","originalName":"Sponsored_Products_adgroup_search_terms_Jan_18_2025.csv","size":6590,"timestamp":"2025-06-15 20:00:18:018"}
{"duration":"13ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"POST","status":400,"timestamp":"2025-06-15 20:00:18:018","url":"/","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"relation \"analysis_jobs\" does not exist","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase query error\u001b[39m","text":"\n        SELECT \n          status,\n          COUNT(*) as count,\n          AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_processing_time\n        FROM analysis_jobs \n        WHERE created_at >= NOW() - INTERVAL '30 days'\n        GROUP BY status\n      ","timestamp":"2025-06-15 20:00:32:032"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting analysis job statistics relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 20:00:32:032"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting upload status relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 20:00:32:032"}
{"duration":"4ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":500,"timestamp":"2025-06-15 20:00:32:032","url":"/status","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 20:00:32:032","url":"/favicon.ico","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 20:00:33:033","url":"/favicon.ico","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 20:00:33:033","url":"/favicon.ico","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to PostgreSQL database\u001b[39m","timestamp":"2025-06-15 20:00:33:033"}
{"error":"relation \"analysis_jobs\" does not exist","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase query error\u001b[39m","text":"\n        SELECT \n          status,\n          COUNT(*) as count,\n          AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_processing_time\n        FROM analysis_jobs \n        WHERE created_at >= NOW() - INTERVAL '30 days'\n        GROUP BY status\n      ","timestamp":"2025-06-15 20:00:33:033"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting analysis job statistics relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 20:00:33:033"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting upload status relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 20:00:33:033"}
{"duration":"20ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":500,"timestamp":"2025-06-15 20:00:33:033","url":"/status","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 20:00:36:036","url":"/","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 20:00:36:036","url":"/static/js/main.115b848b.js","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 20:00:36:036","url":"/static/css/main.09d07e9c.css","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to PostgreSQL database\u001b[39m","timestamp":"2025-06-15 20:00:36:036"}
{"error":"relation \"analysis_jobs\" does not exist","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase query error\u001b[39m","text":"\n        SELECT \n          status,\n          COUNT(*) as count,\n          AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_processing_time\n        FROM analysis_jobs \n        WHERE created_at >= NOW() - INTERVAL '30 days'\n        GROUP BY status\n      ","timestamp":"2025-06-15 20:00:36:036"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting analysis job statistics relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 20:00:36:036"}
{"code":"42P01","file":"parse_relation.c","length":113,"level":"\u001b[31merror\u001b[39m","line":"1392","message":"\u001b[31mError getting upload status relation \"analysis_jobs\" does not exist\u001b[39m","name":"error","position":"164","routine":"parserOpenTable","severity":"ERROR","stack":"error: relation \"analysis_jobs\" does not exist\n    at /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async query (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/config/database.js:29:17)\n    at async AnalysisJob.getStatistics (/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/models/AnalysisJob.js:190:22)\n    at async /home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/src/routes/upload.js:123:19","timestamp":"2025-06-15 20:00:36:036"}
{"duration":"18ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":500,"timestamp":"2025-06-15 20:00:36:036","url":"/status","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 20:00:36:036","url":"/favicon.ico","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 20:00:37:037","url":"/favicon.ico","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":200,"timestamp":"2025-06-15 20:00:37:037","url":"/upload","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 20:00:37:037","url":"/static/js/main.115b848b.js","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 20:00:37:037","url":"/static/css/main.09d07e9c.css","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"GET","status":304,"timestamp":"2025-06-15 20:00:37:037","url":"/favicon.ico","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"filename":"csvFile-1749997842321-721624523.csv","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mFile upload received\u001b[39m","mimetype":"text/csv","originalName":"Sponsored_Products_adgroup_search_terms_Jan_18_2025.csv","size":6590,"timestamp":"2025-06-15 20:00:42:042"}
{"duration":"5ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP Request\u001b[39m","method":"POST","status":400,"timestamp":"2025-06-15 20:00:42:042","url":"/","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
