const { Queue, Worker } = require('bullmq');
const { redisClient } = require('../config/redis');
const logger = require('../utils/logger');
const csvProcessorService = require('./csvProcessorService');
const analysisService = require('./analysisService');
const optimizationService = require('./optimizationService');

// Queue configurations
const queueConfig = {
  connection: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
  },
  defaultJobOptions: {
    removeOnComplete: 50,
    removeOnFail: 20,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
};

// Create queues
const csvProcessingQueue = new Queue('csv-processing', queueConfig);
const analysisQueue = new Queue('analysis', queueConfig);
const optimizationQueue = new Queue('optimization', queueConfig);

// CSV Processing Worker
const csvProcessingWorker = new Worker(
  'csv-processing',
  async (job) => {
    const { jobId, filePath } = job.data;
    logger.info('Starting CSV processing job', { jobId, filePath });

    try {
      // Update job progress
      await job.updateProgress(10);
      
      // Process CSV file
      const result = await csvProcessorService.processCSVFile(jobId, filePath, (progress) => {
        job.updateProgress(Math.min(progress, 90));
      });

      await job.updateProgress(100);
      logger.info('CSV processing job completed', { jobId, rowsProcessed: result.rowsProcessed });
      
      return result;
    } catch (error) {
      logger.error('CSV processing job failed', { jobId, error: error.message });
      throw error;
    }
  },
  {
    connection: {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
    },
    concurrency: 2, // Process 2 CSV files simultaneously
  }
);

// Analysis Worker
const analysisWorker = new Worker(
  'analysis',
  async (job) => {
    const { jobId, options } = job.data;
    logger.info('Starting analysis job', { jobId, options });

    try {
      await job.updateProgress(10);
      
      // Perform analysis
      const result = await analysisService.performAnalysis(jobId, options, (progress) => {
        job.updateProgress(Math.min(progress, 90));
      });

      await job.updateProgress(100);
      logger.info('Analysis job completed', { jobId });
      
      return result;
    } catch (error) {
      logger.error('Analysis job failed', { jobId, error: error.message });
      throw error;
    }
  },
  {
    connection: {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
    },
    concurrency: 3, // Process 3 analysis jobs simultaneously
  }
);

// Optimization Worker
const optimizationWorker = new Worker(
  'optimization',
  async (job) => {
    const { jobId, options } = job.data;
    logger.info('Starting optimization job', { jobId, options });

    try {
      await job.updateProgress(10);
      
      // Generate optimizations
      const result = await optimizationService.generateOptimizations(jobId, options, (progress) => {
        job.updateProgress(Math.min(progress, 90));
      });

      await job.updateProgress(100);
      logger.info('Optimization job completed', { jobId });
      
      return result;
    } catch (error) {
      logger.error('Optimization job failed', { jobId, error: error.message });
      throw error;
    }
  },
  {
    connection: {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
    },
    concurrency: 2, // Process 2 optimization jobs simultaneously
  }
);

// Worker event handlers
const setupWorkerEvents = (worker, workerName) => {
  worker.on('completed', (job) => {
    logger.info(`${workerName} job completed`, { 
      jobId: job.id, 
      duration: Date.now() - job.timestamp 
    });
  });

  worker.on('failed', (job, err) => {
    logger.error(`${workerName} job failed`, { 
      jobId: job?.id, 
      error: err.message,
      attempts: job?.attemptsMade 
    });
  });

  worker.on('progress', (job, progress) => {
    logger.debug(`${workerName} job progress`, { 
      jobId: job.id, 
      progress 
    });
  });

  worker.on('error', (err) => {
    logger.error(`${workerName} worker error`, err);
  });
};

// Setup event handlers for all workers
setupWorkerEvents(csvProcessingWorker, 'CSV Processing');
setupWorkerEvents(analysisWorker, 'Analysis');
setupWorkerEvents(optimizationWorker, 'Optimization');

// Queue service methods
const queueService = {
  // Add CSV processing job
  async addCSVProcessingJob(jobId, filePath, priority = 0) {
    try {
      const job = await csvProcessingQueue.add(
        'process-csv',
        { jobId, filePath },
        { 
          priority,
          jobId: `csv-${jobId}` // Use custom job ID for tracking
        }
      );
      
      logger.info('CSV processing job added to queue', { jobId, queueJobId: job.id });
      return job;
    } catch (error) {
      logger.error('Error adding CSV processing job to queue', { jobId, error });
      throw error;
    }
  },

  // Add analysis job
  async addAnalysisJob(jobId, options = {}, priority = 0) {
    try {
      const job = await analysisQueue.add(
        'perform-analysis',
        { jobId, options },
        { 
          priority,
          jobId: `analysis-${jobId}`
        }
      );
      
      logger.info('Analysis job added to queue', { jobId, queueJobId: job.id });
      return job;
    } catch (error) {
      logger.error('Error adding analysis job to queue', { jobId, error });
      throw error;
    }
  },

  // Add optimization job
  async addOptimizationJob(jobId, options = {}, priority = 0) {
    try {
      const job = await optimizationQueue.add(
        'generate-optimizations',
        { jobId, options },
        { 
          priority,
          jobId: `optimization-${jobId}`
        }
      );
      
      logger.info('Optimization job added to queue', { jobId, queueJobId: job.id });
      return job;
    } catch (error) {
      logger.error('Error adding optimization job to queue', { jobId, error });
      throw error;
    }
  },

  // Get job status from queue
  async getJobStatus(queueJobId, queueType = 'csv-processing') {
    try {
      let queue;
      switch (queueType) {
        case 'csv-processing':
          queue = csvProcessingQueue;
          break;
        case 'analysis':
          queue = analysisQueue;
          break;
        case 'optimization':
          queue = optimizationQueue;
          break;
        default:
          throw new Error(`Unknown queue type: ${queueType}`);
      }

      const job = await queue.getJob(queueJobId);
      if (!job) {
        return null;
      }

      return {
        id: job.id,
        name: job.name,
        data: job.data,
        progress: job.progress,
        state: await job.getState(),
        createdAt: new Date(job.timestamp),
        processedAt: job.processedOn ? new Date(job.processedOn) : null,
        finishedAt: job.finishedOn ? new Date(job.finishedOn) : null,
        failedReason: job.failedReason,
        attempts: job.attemptsMade,
        maxAttempts: job.opts.attempts
      };
    } catch (error) {
      logger.error('Error getting job status from queue', { queueJobId, queueType, error });
      throw error;
    }
  },

  // Get queue statistics
  async getQueueStats() {
    try {
      const [csvStats, analysisStats, optimizationStats] = await Promise.all([
        csvProcessingQueue.getJobCounts(),
        analysisQueue.getJobCounts(),
        optimizationQueue.getJobCounts()
      ]);

      return {
        csvProcessing: csvStats,
        analysis: analysisStats,
        optimization: optimizationStats
      };
    } catch (error) {
      logger.error('Error getting queue statistics', error);
      throw error;
    }
  },

  // Clean completed jobs
  async cleanQueues() {
    try {
      await Promise.all([
        csvProcessingQueue.clean(24 * 60 * 60 * 1000, 100, 'completed'), // Clean completed jobs older than 24 hours
        csvProcessingQueue.clean(7 * 24 * 60 * 60 * 1000, 50, 'failed'), // Clean failed jobs older than 7 days
        analysisQueue.clean(24 * 60 * 60 * 1000, 100, 'completed'),
        analysisQueue.clean(7 * 24 * 60 * 60 * 1000, 50, 'failed'),
        optimizationQueue.clean(24 * 60 * 60 * 1000, 100, 'completed'),
        optimizationQueue.clean(7 * 24 * 60 * 60 * 1000, 50, 'failed')
      ]);
      
      logger.info('Queue cleanup completed');
    } catch (error) {
      logger.error('Error cleaning queues', error);
      throw error;
    }
  },

  // Graceful shutdown
  async shutdown() {
    try {
      logger.info('Shutting down queue workers...');
      
      await Promise.all([
        csvProcessingWorker.close(),
        analysisWorker.close(),
        optimizationWorker.close()
      ]);
      
      await Promise.all([
        csvProcessingQueue.close(),
        analysisQueue.close(),
        optimizationQueue.close()
      ]);
      
      logger.info('Queue workers shut down successfully');
    } catch (error) {
      logger.error('Error shutting down queue workers', error);
      throw error;
    }
  }
};

module.exports = queueService;
