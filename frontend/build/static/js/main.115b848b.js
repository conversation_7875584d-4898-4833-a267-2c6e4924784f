/*! For license information please see main.115b848b.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,l={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)o.call(t,r)&&!s.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:l,_owner:i.current}}t.Fragment=l,t.jsx=u,t.jsxs=u},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,v={};function g(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}g.prototype.isReactComponent={},g.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},g.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=g.prototype;var w=b.prototype=new y;w.constructor=b,m(w,g.prototype),w.isPureReactComponent=!0;var x=Array.isArray,S=Object.prototype.hasOwnProperty,k={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function j(e,t,r){var a,l={},o=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(o=""+t.key),t)S.call(t,a)&&!E.hasOwnProperty(a)&&(l[a]=t[a]);var s=arguments.length-2;if(1===s)l.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];l.children=u}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===l[a]&&(l[a]=s[a]);return{$$typeof:n,type:e,key:o,ref:i,props:l,_owner:k.current}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var N=/\/+/g;function _(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function P(e,t,a,l,o){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s=!1;if(null===e)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return o=o(s=e),e=""===l?"."+_(s,0):l,x(o)?(a="",null!=e&&(a=e.replace(N,"$&/")+"/"),P(o,t,a,"",(function(e){return e}))):null!=o&&(C(o)&&(o=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,a+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(N,"$&/")+"/")+e)),t.push(o)),1;if(s=0,l=""===l?".":l+":",x(e))for(var u=0;u<e.length;u++){var c=l+_(i=e[u],u);s+=P(i,t,a,c,o)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(i=e.next()).done;)s+=P(i=i.value,t,a,c=l+_(i,u++),o);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function R(e,t,n){if(null==e)return e;var r=[],a=0;return P(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function O(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var T={current:null},L={transition:null},z={ReactCurrentDispatcher:T,ReactCurrentBatchConfig:L,ReactCurrentOwner:k};function F(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:R,forEach:function(e,t,n){R(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return R(e,(function(){t++})),t},toArray:function(e){return R(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=g,t.Fragment=a,t.Profiler=o,t.PureComponent=b,t.StrictMode=l,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=z,t.act=F,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),l=e.key,o=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,i=k.current),void 0!==t.key&&(l=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)S.call(t,u)&&!E.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];a.children=s}return{$$typeof:n,type:e.type,key:l,ref:o,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=j,t.createFactory=function(e){var t=j.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:O}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=L.transition;L.transition={};try{e()}finally{L.transition=t}},t.unstable_act=F,t.useCallback=function(e,t){return T.current.useCallback(e,t)},t.useContext=function(e){return T.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return T.current.useDeferredValue(e)},t.useEffect=function(e,t){return T.current.useEffect(e,t)},t.useId=function(){return T.current.useId()},t.useImperativeHandle=function(e,t,n){return T.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return T.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return T.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return T.current.useMemo(e,t)},t.useReducer=function(e,t,n){return T.current.useReducer(e,t,n)},t.useRef=function(e){return T.current.useRef(e)},t.useState=function(e){return T.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return T.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return T.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var i=2*(r+1)-1,s=e[i],u=i+1,c=e[u];if(0>l(s,n))u<a&&0>l(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[i]=n,r=i);else{if(!(u<a&&0>l(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,v=!1,g="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function x(e){if(v=!1,w(e),!m)if(null!==r(u))m=!0,L(S);else{var t=r(c);null!==t&&z(x,t.startTime-e)}}function S(e,n){m=!1,v&&(v=!1,y(C),C=-1),h=!0;var l=p;try{for(w(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!P());){var o=f.callback;if("function"===typeof o){f.callback=null,p=f.priorityLevel;var i=o(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof i?f.callback=i:f===r(u)&&a(u),w(n)}else a(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&z(x,d.startTime-n),s=!1}return s}finally{f=null,p=l,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,E=!1,j=null,C=-1,N=5,_=-1;function P(){return!(t.unstable_now()-_<N)}function R(){if(null!==j){var e=t.unstable_now();_=e;var n=!0;try{n=j(!0,e)}finally{n?k():(E=!1,j=null)}}else E=!1}if("function"===typeof b)k=function(){b(R)};else if("undefined"!==typeof MessageChannel){var O=new MessageChannel,T=O.port2;O.port1.onmessage=R,k=function(){T.postMessage(null)}}else k=function(){g(R,0)};function L(e){j=e,E||(E=!0,k())}function z(e,n){C=g((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,L(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,l){var o=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?o+l:o:l=o,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>o?(e.sortIndex=l,n(c,e),null===r(u)&&e===r(c)&&(v?(y(C),C=-1):v=!0,z(x,l-o))):(e.sortIndex=i,n(u,e),m||h||(m=!0,L(S))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),a=n(853);function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,i={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(i[e]=t,e=0;e<t.length;e++)o.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,a,l,o){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=o}var v={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){v[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];v[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){v[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){v[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){v[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){v[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){v[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){v[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){v[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var g=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=v.hasOwnProperty(t)?v[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){v[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),v.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){v[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var w=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,x=Symbol.for("react.element"),S=Symbol.for("react.portal"),k=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),j=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),N=Symbol.for("react.context"),_=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),R=Symbol.for("react.suspense_list"),O=Symbol.for("react.memo"),T=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var L=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var z=Symbol.iterator;function F(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=z&&e[z]||e["@@iterator"])?e:null}var D,A=Object.assign;function U(e){if(void 0===D)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);D=t&&t[1]||""}return"\n"+D+e}var M=!1;function I(e,t){if(!e||M)return"";M=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var a=u.stack.split("\n"),l=r.stack.split("\n"),o=a.length-1,i=l.length-1;1<=o&&0<=i&&a[o]!==l[i];)i--;for(;1<=o&&0<=i;o--,i--)if(a[o]!==l[i]){if(1!==o||1!==i)do{if(o--,0>--i||a[o]!==l[i]){var s="\n"+a[o].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=o&&0<=i);break}}}finally{M=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?U(e):""}function B(e){switch(e.tag){case 5:return U(e.type);case 16:return U("Lazy");case 13:return U("Suspense");case 19:return U("SuspenseList");case 0:case 2:case 15:return e=I(e.type,!1);case 11:return e=I(e.type.render,!1);case 1:return e=I(e.type,!0);default:return""}}function V(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case k:return"Fragment";case S:return"Portal";case j:return"Profiler";case E:return"StrictMode";case P:return"Suspense";case R:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case N:return(e.displayName||"Context")+".Consumer";case C:return(e._context.displayName||"Context")+".Provider";case _:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case O:return null!==(t=e.displayName||null)?t:V(e.type)||"Memo";case T:t=e._payload,e=e._init;try{return V(e(t))}catch(n){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return V(t);case 8:return t===E?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function $(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function H(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function q(e){e._valueTracker||(e._valueTracker=function(e){var t=H(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=H(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function J(e,t){var n=t.checked;return A({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function X(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=$(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Y(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function G(e,t){Y(e,t);var n=$(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,$(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+$(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(l(91));return A({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(l(92));if(te(n)){if(1<n.length)throw Error(l(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:$(n)}}function le(e,t){var n=$(t.value),r=$(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function oe(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ce(e,t)}))}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ve(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach((function(e){he.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]}))}));var ge=A({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ge[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(l(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(l(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(l(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function xe(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,ke=null,Ee=null;function je(e){if(e=ba(e)){if("function"!==typeof Se)throw Error(l(280));var t=e.stateNode;t&&(t=xa(t),Se(e.stateNode,e.type,t))}}function Ce(e){ke?Ee?Ee.push(e):Ee=[e]:ke=e}function Ne(){if(ke){var e=ke,t=Ee;if(Ee=ke=null,je(e),t)for(e=0;e<t.length;e++)je(t[e])}}function _e(e,t){return e(t)}function Pe(){}var Re=!1;function Oe(e,t,n){if(Re)return e(t,n);Re=!0;try{return _e(e,t,n)}finally{Re=!1,(null!==ke||null!==Ee)&&(Pe(),Ne())}}function Te(e,t){var n=e.stateNode;if(null===n)return null;var r=xa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(l(231,t,typeof n));return n}var Le=!1;if(c)try{var ze={};Object.defineProperty(ze,"passive",{get:function(){Le=!0}}),window.addEventListener("test",ze,ze),window.removeEventListener("test",ze,ze)}catch(ce){Le=!1}function Fe(e,t,n,r,a,l,o,i,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var De=!1,Ae=null,Ue=!1,Me=null,Ie={onError:function(e){De=!0,Ae=e}};function Be(e,t,n,r,a,l,o,i,s){De=!1,Ae=null,Fe.apply(Ie,arguments)}function Ve(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function $e(e){if(Ve(e)!==e)throw Error(l(188))}function He(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ve(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return $e(a),e;if(o===r)return $e(a),t;o=o.sibling}throw Error(l(188))}if(n.return!==r.return)n=a,r=o;else{for(var i=!1,s=a.child;s;){if(s===n){i=!0,n=a,r=o;break}if(s===r){i=!0,r=a,n=o;break}s=s.sibling}if(!i){for(s=o.child;s;){if(s===n){i=!0,n=o,r=a;break}if(s===r){i=!0,r=o,n=a;break}s=s.sibling}if(!i)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(e))?qe(e):null}function qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=qe(e);if(null!==t)return t;e=e.sibling}return null}var Qe=a.unstable_scheduleCallback,Ke=a.unstable_cancelCallback,Je=a.unstable_shouldYield,Xe=a.unstable_requestPaint,Ye=a.unstable_now,Ge=a.unstable_getCurrentPriorityLevel,Ze=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,lt=null;var ot=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(it(e)/st|0)|0},it=Math.log,st=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,l=e.pingedLanes,o=268435455&n;if(0!==o){var i=o&~a;0!==i?r=dt(i):0!==(l&=o)&&(r=dt(l))}else 0!==(o=n&~a)?r=dt(o):0!==l&&(r=dt(l));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(l=t&-t)||16===a&&0!==(4194240&l)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-ot(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function vt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-ot(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function wt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var xt,St,kt,Et,jt,Ct=!1,Nt=[],_t=null,Pt=null,Rt=null,Ot=new Map,Tt=new Map,Lt=[],zt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ft(e,t){switch(e){case"focusin":case"focusout":_t=null;break;case"dragenter":case"dragleave":Pt=null;break;case"mouseover":case"mouseout":Rt=null;break;case"pointerover":case"pointerout":Ot.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Tt.delete(t.pointerId)}}function Dt(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=ba(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function At(e){var t=ya(e.target);if(null!==t){var n=Ve(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void jt(e.priority,(function(){kt(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Ut(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Jt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ba(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);we=r,n.target.dispatchEvent(r),we=null,t.shift()}return!0}function Mt(e,t,n){Ut(e)&&n.delete(t)}function It(){Ct=!1,null!==_t&&Ut(_t)&&(_t=null),null!==Pt&&Ut(Pt)&&(Pt=null),null!==Rt&&Ut(Rt)&&(Rt=null),Ot.forEach(Mt),Tt.forEach(Mt)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,Ct||(Ct=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,It)))}function Vt(e){function t(t){return Bt(t,e)}if(0<Nt.length){Bt(Nt[0],e);for(var n=1;n<Nt.length;n++){var r=Nt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==_t&&Bt(_t,e),null!==Pt&&Bt(Pt,e),null!==Rt&&Bt(Rt,e),Ot.forEach(t),Tt.forEach(t),n=0;n<Lt.length;n++)(r=Lt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&null===(n=Lt[0]).blockedOn;)At(n),null===n.blockedOn&&Lt.shift()}var Wt=w.ReactCurrentBatchConfig,$t=!0;function Ht(e,t,n,r){var a=bt,l=Wt.transition;Wt.transition=null;try{bt=1,Qt(e,t,n,r)}finally{bt=a,Wt.transition=l}}function qt(e,t,n,r){var a=bt,l=Wt.transition;Wt.transition=null;try{bt=4,Qt(e,t,n,r)}finally{bt=a,Wt.transition=l}}function Qt(e,t,n,r){if($t){var a=Jt(e,t,n,r);if(null===a)$r(e,t,r,Kt,n),Ft(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return _t=Dt(_t,e,t,n,r,a),!0;case"dragenter":return Pt=Dt(Pt,e,t,n,r,a),!0;case"mouseover":return Rt=Dt(Rt,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return Ot.set(l,Dt(Ot.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,Tt.set(l,Dt(Tt.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Ft(e,r),4&t&&-1<zt.indexOf(e)){for(;null!==a;){var l=ba(a);if(null!==l&&xt(l),null===(l=Jt(e,t,n,r))&&$r(e,t,r,Kt,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else $r(e,t,r,null,n)}}var Kt=null;function Jt(e,t,n,r){if(Kt=null,null!==(e=ya(e=xe(r))))if(null===(t=Ve(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ge()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Yt=null,Gt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Gt,r=n.length,a="value"in Yt?Yt.value:Yt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[l-t];t++);return Zt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,l){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return A(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var ln,on,sn,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=A({},un,{view:0,detail:0}),fn=an(dn),pn=A({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(ln=e.screenX-sn.screenX,on=e.screenY-sn.screenY):on=ln=0,sn=e),ln)},movementY:function(e){return"movementY"in e?e.movementY:on}}),hn=an(pn),mn=an(A({},pn,{dataTransfer:0})),vn=an(A({},dn,{relatedTarget:0})),gn=an(A({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=A({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(yn),wn=an(A({},un,{data:0})),xn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function En(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=kn[e])&&!!t[e]}function jn(){return En}var Cn=A({},dn,{key:function(e){if(e.key){var t=xn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Nn=an(Cn),_n=an(A({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Pn=an(A({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jn})),Rn=an(A({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),On=A({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Tn=an(On),Ln=[9,13,27,32],zn=c&&"CompositionEvent"in window,Fn=null;c&&"documentMode"in document&&(Fn=document.documentMode);var Dn=c&&"TextEvent"in window&&!Fn,An=c&&(!zn||Fn&&8<Fn&&11>=Fn),Un=String.fromCharCode(32),Mn=!1;function In(e,t){switch(e){case"keyup":return-1!==Ln.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Vn=!1;var Wn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function $n(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wn[e.type]:"textarea"===t}function Hn(e,t,n,r){Ce(r),0<(t=qr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qn=null,Qn=null;function Kn(e){Ur(e,0)}function Jn(e){if(Q(wa(e)))return e}function Xn(e,t){if("change"===e)return t}var Yn=!1;if(c){var Gn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Gn=Zn}else Gn=!1;Yn=Gn&&(!document.documentMode||9<document.documentMode)}function tr(){qn&&(qn.detachEvent("onpropertychange",nr),Qn=qn=null)}function nr(e){if("value"===e.propertyName&&Jn(Qn)){var t=[];Hn(t,Qn,e,xe(e)),Oe(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Qn=n,(qn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Jn(Qn)}function lr(e,t){if("click"===e)return Jn(t)}function or(e,t){if("input"===e||"change"===e)return Jn(t)}var ir="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(ir(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!ir(e[a],t[a]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,l=Math.min(r.start,a);r=void 0===r.end?l:Math.min(r.end,a),!e.extend&&l>r&&(a=r,r=l,l=a),a=cr(n,l);var o=cr(n,r);a&&o&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,vr=null,gr=null,yr=null,br=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==vr||vr!==K(r)||("selectionStart"in(r=vr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&sr(yr,r)||(yr=r,0<(r=qr(gr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=vr)))}function xr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:xr("Animation","AnimationEnd"),animationiteration:xr("Animation","AnimationIteration"),animationstart:xr("Animation","AnimationStart"),transitionend:xr("Transition","TransitionEnd")},kr={},Er={};function jr(e){if(kr[e])return kr[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Er)return kr[e]=n[t];return e}c&&(Er=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var Cr=jr("animationend"),Nr=jr("animationiteration"),_r=jr("animationstart"),Pr=jr("transitionend"),Rr=new Map,Or="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Tr(e,t){Rr.set(e,t),s(t,[e])}for(var Lr=0;Lr<Or.length;Lr++){var zr=Or[Lr];Tr(zr.toLowerCase(),"on"+(zr[0].toUpperCase()+zr.slice(1)))}Tr(Cr,"onAnimationEnd"),Tr(Nr,"onAnimationIteration"),Tr(_r,"onAnimationStart"),Tr("dblclick","onDoubleClick"),Tr("focusin","onFocus"),Tr("focusout","onBlur"),Tr(Pr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Fr));function Ar(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,o,i,s,u){if(Be.apply(this,arguments),De){if(!De)throw Error(l(198));var c=Ae;De=!1,Ae=null,Ue||(Ue=!0,Me=c)}}(r,t,void 0,e),e.currentTarget=null}function Ur(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==l&&a.isPropagationStopped())break e;Ar(a,i,u),l=s}else for(o=0;o<r.length;o++){if(s=(i=r[o]).instance,u=i.currentTarget,i=i.listener,s!==l&&a.isPropagationStopped())break e;Ar(a,i,u),l=s}}}if(Ue)throw e=Me,Ue=!1,Me=null,e}function Mr(e,t){var n=t[ma];void 0===n&&(n=t[ma]=new Set);var r=e+"__bubble";n.has(r)||(Wr(t,e,2,!1),n.add(r))}function Ir(e,t,n){var r=0;t&&(r|=4),Wr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function Vr(e){if(!e[Br]){e[Br]=!0,o.forEach((function(t){"selectionchange"!==t&&(Dr.has(t)||Ir(t,!1,e),Ir(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,Ir("selectionchange",!1,t))}}function Wr(e,t,n,r){switch(Xt(t)){case 1:var a=Ht;break;case 4:a=qt;break;default:a=Qt}n=a.bind(null,t,n,e),a=void 0,!Le||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function $r(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===o)for(o=r.return;null!==o;){var s=o.tag;if((3===s||4===s)&&((s=o.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;o=o.return}for(;null!==i;){if(null===(o=ya(i)))return;if(5===(s=o.tag)||6===s){r=l=o;continue e}i=i.parentNode}}r=r.return}Oe((function(){var r=l,a=xe(n),o=[];e:{var i=Rr.get(e);if(void 0!==i){var s=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=Nn;break;case"focusin":u="focus",s=vn;break;case"focusout":u="blur",s=vn;break;case"beforeblur":case"afterblur":s=vn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Pn;break;case Cr:case Nr:case _r:s=gn;break;case Pr:s=Rn;break;case"scroll":s=fn;break;case"wheel":s=Tn;break;case"copy":case"cut":case"paste":s=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=_n}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==i?i+"Capture":null:i;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Te(h,f))&&c.push(Hr(h,m,p)))),d)break;h=h.return}0<c.length&&(i=new s(i,u,null,n,a),o.push({event:i,listeners:c}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===we||!(u=n.relatedTarget||n.fromElement)||!ya(u)&&!u[ha])&&(s||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?ya(u):null)&&(u!==(d=Ve(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=_n,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?i:wa(s),p=null==u?i:wa(u),(i=new c(m,h+"leave",s,n,a)).target=d,i.relatedTarget=p,m=null,ya(a)===r&&((c=new c(f,h+"enter",u,n,a)).target=p,c.relatedTarget=d,m=c),d=m,s&&u)e:{for(f=u,h=0,p=c=s;p;p=Qr(p))h++;for(p=0,m=f;m;m=Qr(m))p++;for(;0<h-p;)c=Qr(c),h--;for(;0<p-h;)f=Qr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=Qr(c),f=Qr(f)}c=null}else c=null;null!==s&&Kr(o,i,s,c,!1),null!==u&&null!==d&&Kr(o,d,u,c,!0)}if("select"===(s=(i=r?wa(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===s&&"file"===i.type)var v=Xn;else if($n(i))if(Yn)v=or;else{v=ar;var g=rr}else(s=i.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(v=lr);switch(v&&(v=v(e,r))?Hn(o,v,n,a):(g&&g(e,i,r),"focusout"===e&&(g=i._wrapperState)&&g.controlled&&"number"===i.type&&ee(i,"number",i.value)),g=r?wa(r):window,e){case"focusin":($n(g)||"true"===g.contentEditable)&&(vr=g,gr=r,yr=null);break;case"focusout":yr=gr=vr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,wr(o,n,a);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":wr(o,n,a)}var y;if(zn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Vn?In(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(An&&"ko"!==n.locale&&(Vn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Vn&&(y=en()):(Gt="value"in(Yt=a)?Yt.value:Yt.textContent,Vn=!0)),0<(g=qr(r,b)).length&&(b=new wn(b,e,null,n,a),o.push({event:b,listeners:g}),y?b.data=y:null!==(y=Bn(n))&&(b.data=y))),(y=Dn?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:(Mn=!0,Un);case"textInput":return(e=t.data)===Un&&Mn?null:e;default:return null}}(e,n):function(e,t){if(Vn)return"compositionend"===e||!zn&&In(e,t)?(e=en(),Zt=Gt=Yt=null,Vn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return An&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=qr(r,"onBeforeInput")).length&&(a=new wn("onBeforeInput","beforeinput",null,n,a),o.push({event:a,listeners:r}),a.data=y))}Ur(o,t)}))}function Hr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;5===a.tag&&null!==l&&(a=l,null!=(l=Te(e,n))&&r.unshift(Hr(e,l,a)),null!=(l=Te(e,t))&&r.push(Hr(e,l,a))),e=e.return}return r}function Qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,a){for(var l=t._reactName,o=[];null!==n&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(null!==s&&s===r)break;5===i.tag&&null!==u&&(i=u,a?null!=(s=Te(n,l))&&o.unshift(Hr(n,s,i)):a||null!=(s=Te(n,l))&&o.push(Hr(n,s,i))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Jr=/\r\n?/g,Xr=/\u0000|\uFFFD/g;function Yr(e){return("string"===typeof e?e:""+e).replace(Jr,"\n").replace(Xr,"")}function Gr(e,t,n){if(t=Yr(t),Yr(e)!==t&&n)throw Error(l(425))}function Zr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,la="function"===typeof Promise?Promise:void 0,oa="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof la?function(e){return la.resolve(null).then(e).catch(ia)}:ra;function ia(e){setTimeout((function(){throw e}))}function sa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Vt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Vt(t)}function ua(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ha="__reactContainer$"+da,ma="__reactEvents$"+da,va="__reactListeners$"+da,ga="__reactHandles$"+da;function ya(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ha]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[fa])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function ba(e){return!(e=e[fa]||e[ha])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function wa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(l(33))}function xa(e){return e[pa]||null}var Sa=[],ka=-1;function Ea(e){return{current:e}}function ja(e){0>ka||(e.current=Sa[ka],Sa[ka]=null,ka--)}function Ca(e,t){ka++,Sa[ka]=e.current,e.current=t}var Na={},_a=Ea(Na),Pa=Ea(!1),Ra=Na;function Oa(e,t){var n=e.type.contextTypes;if(!n)return Na;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,l={};for(a in n)l[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Ta(e){return null!==(e=e.childContextTypes)&&void 0!==e}function La(){ja(Pa),ja(_a)}function za(e,t,n){if(_a.current!==Na)throw Error(l(168));Ca(_a,t),Ca(Pa,n)}function Fa(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(l(108,W(e)||"Unknown",a));return A({},n,r)}function Da(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Na,Ra=_a.current,Ca(_a,e),Ca(Pa,Pa.current),!0}function Aa(e,t,n){var r=e.stateNode;if(!r)throw Error(l(169));n?(e=Fa(e,t,Ra),r.__reactInternalMemoizedMergedChildContext=e,ja(Pa),ja(_a),Ca(_a,e)):ja(Pa),Ca(Pa,n)}var Ua=null,Ma=!1,Ia=!1;function Ba(e){null===Ua?Ua=[e]:Ua.push(e)}function Va(){if(!Ia&&null!==Ua){Ia=!0;var e=0,t=bt;try{var n=Ua;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ua=null,Ma=!1}catch(a){throw null!==Ua&&(Ua=Ua.slice(e+1)),Qe(Ze,Va),a}finally{bt=t,Ia=!1}}return null}var Wa=[],$a=0,Ha=null,qa=0,Qa=[],Ka=0,Ja=null,Xa=1,Ya="";function Ga(e,t){Wa[$a++]=qa,Wa[$a++]=Ha,Ha=e,qa=t}function Za(e,t,n){Qa[Ka++]=Xa,Qa[Ka++]=Ya,Qa[Ka++]=Ja,Ja=e;var r=Xa;e=Ya;var a=32-ot(r)-1;r&=~(1<<a),n+=1;var l=32-ot(t)+a;if(30<l){var o=a-a%5;l=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Xa=1<<32-ot(t)+a|n<<a|r,Ya=l+e}else Xa=1<<l|n<<a|r,Ya=e}function el(e){null!==e.return&&(Ga(e,1),Za(e,1,0))}function tl(e){for(;e===Ha;)Ha=Wa[--$a],Wa[$a]=null,qa=Wa[--$a],Wa[$a]=null;for(;e===Ja;)Ja=Qa[--Ka],Qa[Ka]=null,Ya=Qa[--Ka],Qa[Ka]=null,Xa=Qa[--Ka],Qa[Ka]=null}var nl=null,rl=null,al=!1,ll=null;function ol(e,t){var n=Ou(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function il(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,nl=e,rl=ua(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,nl=e,rl=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ja?{id:Xa,overflow:Ya}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Ou(18,null,null,0)).stateNode=t,n.return=e,e.child=n,nl=e,rl=null,!0);default:return!1}}function sl(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ul(e){if(al){var t=rl;if(t){var n=t;if(!il(e,t)){if(sl(e))throw Error(l(418));t=ua(n.nextSibling);var r=nl;t&&il(e,t)?ol(r,n):(e.flags=-4097&e.flags|2,al=!1,nl=e)}}else{if(sl(e))throw Error(l(418));e.flags=-4097&e.flags|2,al=!1,nl=e}}}function cl(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;nl=e}function dl(e){if(e!==nl)return!1;if(!al)return cl(e),al=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=rl)){if(sl(e))throw fl(),Error(l(418));for(;t;)ol(e,t),t=ua(t.nextSibling)}if(cl(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){rl=ua(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}rl=null}}else rl=nl?ua(e.stateNode.nextSibling):null;return!0}function fl(){for(var e=rl;e;)e=ua(e.nextSibling)}function pl(){rl=nl=null,al=!1}function hl(e){null===ll?ll=[e]:ll.push(e)}var ml=w.ReactCurrentBatchConfig;function vl(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(l(309));var r=n.stateNode}if(!r)throw Error(l(147,e));var a=r,o=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=a.refs;null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!==typeof e)throw Error(l(284));if(!n._owner)throw Error(l(290,e))}return e}function gl(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yl(e){return(0,e._init)(e._payload)}function bl(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Lu(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Au(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var l=n.type;return l===k?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===T&&yl(l)===t.type)?((r=a(t,n.props)).ref=vl(e,t,n),r.return=e,r):((r=zu(n.type,n.key,n.props,null,e.mode,r)).ref=vl(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Uu(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=Fu(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Au(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case x:return(n=zu(t.type,t.key,t.props,null,e.mode,n)).ref=vl(e,null,t),n.return=e,n;case S:return(t=Uu(t,e.mode,n)).return=e,t;case T:return f(e,(0,t._init)(t._payload),n)}if(te(t)||F(t))return(t=Fu(t,e.mode,n,null)).return=e,t;gl(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case x:return n.key===a?u(e,t,n,r):null;case S:return n.key===a?c(e,t,n,r):null;case T:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||F(n))return null!==a?null:d(e,t,n,r,null);gl(e,n)}return null}function h(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case x:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case S:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case T:return h(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||F(r))return d(t,e=e.get(n)||null,r,a,null);gl(t,r)}return null}function m(a,l,i,s){for(var u=null,c=null,d=l,m=l=0,v=null;null!==d&&m<i.length;m++){d.index>m?(v=d,d=null):v=d.sibling;var g=p(a,d,i[m],s);if(null===g){null===d&&(d=v);break}e&&d&&null===g.alternate&&t(a,d),l=o(g,l,m),null===c?u=g:c.sibling=g,c=g,d=v}if(m===i.length)return n(a,d),al&&Ga(a,m),u;if(null===d){for(;m<i.length;m++)null!==(d=f(a,i[m],s))&&(l=o(d,l,m),null===c?u=d:c.sibling=d,c=d);return al&&Ga(a,m),u}for(d=r(a,d);m<i.length;m++)null!==(v=h(d,a,m,i[m],s))&&(e&&null!==v.alternate&&d.delete(null===v.key?m:v.key),l=o(v,l,m),null===c?u=v:c.sibling=v,c=v);return e&&d.forEach((function(e){return t(a,e)})),al&&Ga(a,m),u}function v(a,i,s,u){var c=F(s);if("function"!==typeof c)throw Error(l(150));if(null==(s=c.call(s)))throw Error(l(151));for(var d=c=null,m=i,v=i=0,g=null,y=s.next();null!==m&&!y.done;v++,y=s.next()){m.index>v?(g=m,m=null):g=m.sibling;var b=p(a,m,y.value,u);if(null===b){null===m&&(m=g);break}e&&m&&null===b.alternate&&t(a,m),i=o(b,i,v),null===d?c=b:d.sibling=b,d=b,m=g}if(y.done)return n(a,m),al&&Ga(a,v),c;if(null===m){for(;!y.done;v++,y=s.next())null!==(y=f(a,y.value,u))&&(i=o(y,i,v),null===d?c=y:d.sibling=y,d=y);return al&&Ga(a,v),c}for(m=r(a,m);!y.done;v++,y=s.next())null!==(y=h(m,a,v,y.value,u))&&(e&&null!==y.alternate&&m.delete(null===y.key?v:y.key),i=o(y,i,v),null===d?c=y:d.sibling=y,d=y);return e&&m.forEach((function(e){return t(a,e)})),al&&Ga(a,v),c}return function e(r,l,o,s){if("object"===typeof o&&null!==o&&o.type===k&&null===o.key&&(o=o.props.children),"object"===typeof o&&null!==o){switch(o.$$typeof){case x:e:{for(var u=o.key,c=l;null!==c;){if(c.key===u){if((u=o.type)===k){if(7===c.tag){n(r,c.sibling),(l=a(c,o.props.children)).return=r,r=l;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===T&&yl(u)===c.type){n(r,c.sibling),(l=a(c,o.props)).ref=vl(r,c,o),l.return=r,r=l;break e}n(r,c);break}t(r,c),c=c.sibling}o.type===k?((l=Fu(o.props.children,r.mode,s,o.key)).return=r,r=l):((s=zu(o.type,o.key,o.props,null,r.mode,s)).ref=vl(r,l,o),s.return=r,r=s)}return i(r);case S:e:{for(c=o.key;null!==l;){if(l.key===c){if(4===l.tag&&l.stateNode.containerInfo===o.containerInfo&&l.stateNode.implementation===o.implementation){n(r,l.sibling),(l=a(l,o.children||[])).return=r,r=l;break e}n(r,l);break}t(r,l),l=l.sibling}(l=Uu(o,r.mode,s)).return=r,r=l}return i(r);case T:return e(r,l,(c=o._init)(o._payload),s)}if(te(o))return m(r,l,o,s);if(F(o))return v(r,l,o,s);gl(r,o)}return"string"===typeof o&&""!==o||"number"===typeof o?(o=""+o,null!==l&&6===l.tag?(n(r,l.sibling),(l=a(l,o)).return=r,r=l):(n(r,l),(l=Au(o,r.mode,s)).return=r,r=l),i(r)):n(r,l)}}var wl=bl(!0),xl=bl(!1),Sl=Ea(null),kl=null,El=null,jl=null;function Cl(){jl=El=kl=null}function Nl(e){var t=Sl.current;ja(Sl),e._currentValue=t}function _l(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Pl(e,t){kl=e,jl=El=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bi=!0),e.firstContext=null)}function Rl(e){var t=e._currentValue;if(jl!==e)if(e={context:e,memoizedValue:t,next:null},null===El){if(null===kl)throw Error(l(308));El=e,kl.dependencies={lanes:0,firstContext:e}}else El=El.next=e;return t}var Ol=null;function Tl(e){null===Ol?Ol=[e]:Ol.push(e)}function Ll(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Tl(t)):(n.next=a.next,a.next=n),t.interleaved=n,zl(e,r)}function zl(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Fl=!1;function Dl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Al(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ul(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ml(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&_s)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,zl(e,n)}return null===(a=r.interleaved)?(t.next=t,Tl(r)):(t.next=a.next,a.next=t),r.interleaved=t,zl(e,n)}function Il(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Bl(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?a=l=o:l=l.next=o,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Vl(e,t,n,r){var a=e.updateQueue;Fl=!1;var l=a.firstBaseUpdate,o=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,u=s.next;s.next=null,null===o?l=u:o.next=u,o=s;var c=e.alternate;null!==c&&((i=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===i?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s))}if(null!==l){var d=a.baseState;for(o=0,c=u=s=null,i=l;;){var f=i.lane,p=i.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var h=e,m=i;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=A({},d,f);break e;case 2:Fl=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else p={eventTime:p,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,o|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{o|=a.lane,a=a.next}while(a!==t)}else null===l&&(a.shared.lanes=0);Ds|=o,e.lanes=o,e.memoizedState=d}}function Wl(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(l(191,a));a.call(r)}}}var $l={},Hl=Ea($l),ql=Ea($l),Ql=Ea($l);function Kl(e){if(e===$l)throw Error(l(174));return e}function Jl(e,t){switch(Ca(Ql,t),Ca(ql,e),Ca(Hl,$l),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}ja(Hl),Ca(Hl,t)}function Xl(){ja(Hl),ja(ql),ja(Ql)}function Yl(e){Kl(Ql.current);var t=Kl(Hl.current),n=se(t,e.type);t!==n&&(Ca(ql,e),Ca(Hl,n))}function Gl(e){ql.current===e&&(ja(Hl),ja(ql))}var Zl=Ea(0);function eo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var to=[];function no(){for(var e=0;e<to.length;e++)to[e]._workInProgressVersionPrimary=null;to.length=0}var ro=w.ReactCurrentDispatcher,ao=w.ReactCurrentBatchConfig,lo=0,oo=null,io=null,so=null,uo=!1,co=!1,fo=0,po=0;function ho(){throw Error(l(321))}function mo(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ir(e[n],t[n]))return!1;return!0}function vo(e,t,n,r,a,o){if(lo=o,oo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ro.current=null===e||null===e.memoizedState?Zo:ei,e=n(r,a),co){o=0;do{if(co=!1,fo=0,25<=o)throw Error(l(301));o+=1,so=io=null,t.updateQueue=null,ro.current=ti,e=n(r,a)}while(co)}if(ro.current=Go,t=null!==io&&null!==io.next,lo=0,so=io=oo=null,uo=!1,t)throw Error(l(300));return e}function go(){var e=0!==fo;return fo=0,e}function yo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===so?oo.memoizedState=so=e:so=so.next=e,so}function bo(){if(null===io){var e=oo.alternate;e=null!==e?e.memoizedState:null}else e=io.next;var t=null===so?oo.memoizedState:so.next;if(null!==t)so=t,io=e;else{if(null===e)throw Error(l(310));e={memoizedState:(io=e).memoizedState,baseState:io.baseState,baseQueue:io.baseQueue,queue:io.queue,next:null},null===so?oo.memoizedState=so=e:so=so.next=e}return so}function wo(e,t){return"function"===typeof t?t(e):t}function xo(e){var t=bo(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=io,a=r.baseQueue,o=n.pending;if(null!==o){if(null!==a){var i=a.next;a.next=o.next,o.next=i}r.baseQueue=a=o,n.pending=null}if(null!==a){o=a.next,r=r.baseState;var s=i=null,u=null,c=o;do{var d=c.lane;if((lo&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,i=r):u=u.next=f,oo.lanes|=d,Ds|=d}c=c.next}while(null!==c&&c!==o);null===u?i=r:u.next=s,ir(r,t.memoizedState)||(bi=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{o=a.lane,oo.lanes|=o,Ds|=o,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function So(e){var t=bo(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{o=e(o,i.action),i=i.next}while(i!==a);ir(o,t.memoizedState)||(bi=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function ko(){}function Eo(e,t){var n=oo,r=bo(),a=t(),o=!ir(r.memoizedState,a);if(o&&(r.memoizedState=a,bi=!0),r=r.queue,Do(No.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==so&&1&so.memoizedState.tag){if(n.flags|=2048,Oo(9,Co.bind(null,n,r,a,t),void 0,null),null===Ps)throw Error(l(349));0!==(30&lo)||jo(n,t,a)}return a}function jo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Co(e,t,n,r){t.value=n,t.getSnapshot=r,_o(t)&&Po(e)}function No(e,t,n){return n((function(){_o(t)&&Po(e)}))}function _o(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ir(e,n)}catch(r){return!0}}function Po(e){var t=zl(e,1);null!==t&&nu(t,e,1,-1)}function Ro(e){var t=yo();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:wo,lastRenderedState:e},t.queue=e,e=e.dispatch=Ko.bind(null,oo,e),[t.memoizedState,e]}function Oo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function To(){return bo().memoizedState}function Lo(e,t,n,r){var a=yo();oo.flags|=e,a.memoizedState=Oo(1|t,n,void 0,void 0===r?null:r)}function zo(e,t,n,r){var a=bo();r=void 0===r?null:r;var l=void 0;if(null!==io){var o=io.memoizedState;if(l=o.destroy,null!==r&&mo(r,o.deps))return void(a.memoizedState=Oo(t,n,l,r))}oo.flags|=e,a.memoizedState=Oo(1|t,n,l,r)}function Fo(e,t){return Lo(8390656,8,e,t)}function Do(e,t){return zo(2048,8,e,t)}function Ao(e,t){return zo(4,2,e,t)}function Uo(e,t){return zo(4,4,e,t)}function Mo(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Io(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,zo(4,4,Mo.bind(null,t,e),n)}function Bo(){}function Vo(e,t){var n=bo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wo(e,t){var n=bo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function $o(e,t,n){return 0===(21&lo)?(e.baseState&&(e.baseState=!1,bi=!0),e.memoizedState=n):(ir(n,t)||(n=mt(),oo.lanes|=n,Ds|=n,e.baseState=!0),t)}function Ho(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=ao.transition;ao.transition={};try{e(!1),t()}finally{bt=n,ao.transition=r}}function qo(){return bo().memoizedState}function Qo(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Jo(e))Xo(t,n);else if(null!==(n=Ll(e,t,n,r))){nu(n,e,r,eu()),Yo(n,t,r)}}function Ko(e,t,n){var r=tu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Jo(e))Xo(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var o=t.lastRenderedState,i=l(o,n);if(a.hasEagerState=!0,a.eagerState=i,ir(i,o)){var s=t.interleaved;return null===s?(a.next=a,Tl(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(u){}null!==(n=Ll(e,t,a,r))&&(nu(n,e,r,a=eu()),Yo(n,t,r))}}function Jo(e){var t=e.alternate;return e===oo||null!==t&&t===oo}function Xo(e,t){co=uo=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Yo(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Go={readContext:Rl,useCallback:ho,useContext:ho,useEffect:ho,useImperativeHandle:ho,useInsertionEffect:ho,useLayoutEffect:ho,useMemo:ho,useReducer:ho,useRef:ho,useState:ho,useDebugValue:ho,useDeferredValue:ho,useTransition:ho,useMutableSource:ho,useSyncExternalStore:ho,useId:ho,unstable_isNewReconciler:!1},Zo={readContext:Rl,useCallback:function(e,t){return yo().memoizedState=[e,void 0===t?null:t],e},useContext:Rl,useEffect:Fo,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Lo(4194308,4,Mo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Lo(4194308,4,e,t)},useInsertionEffect:function(e,t){return Lo(4,2,e,t)},useMemo:function(e,t){var n=yo();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=yo();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qo.bind(null,oo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},yo().memoizedState=e},useState:Ro,useDebugValue:Bo,useDeferredValue:function(e){return yo().memoizedState=e},useTransition:function(){var e=Ro(!1),t=e[0];return e=Ho.bind(null,e[1]),yo().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=oo,a=yo();if(al){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===Ps)throw Error(l(349));0!==(30&lo)||jo(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,Fo(No.bind(null,r,o,e),[e]),r.flags|=2048,Oo(9,Co.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=yo(),t=Ps.identifierPrefix;if(al){var n=Ya;t=":"+t+"R"+(n=(Xa&~(1<<32-ot(Xa)-1)).toString(32)+n),0<(n=fo++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=po++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ei={readContext:Rl,useCallback:Vo,useContext:Rl,useEffect:Do,useImperativeHandle:Io,useInsertionEffect:Ao,useLayoutEffect:Uo,useMemo:Wo,useReducer:xo,useRef:To,useState:function(){return xo(wo)},useDebugValue:Bo,useDeferredValue:function(e){return $o(bo(),io.memoizedState,e)},useTransition:function(){return[xo(wo)[0],bo().memoizedState]},useMutableSource:ko,useSyncExternalStore:Eo,useId:qo,unstable_isNewReconciler:!1},ti={readContext:Rl,useCallback:Vo,useContext:Rl,useEffect:Do,useImperativeHandle:Io,useInsertionEffect:Ao,useLayoutEffect:Uo,useMemo:Wo,useReducer:So,useRef:To,useState:function(){return So(wo)},useDebugValue:Bo,useDeferredValue:function(e){var t=bo();return null===io?t.memoizedState=e:$o(t,io.memoizedState,e)},useTransition:function(){return[So(wo)[0],bo().memoizedState]},useMutableSource:ko,useSyncExternalStore:Eo,useId:qo,unstable_isNewReconciler:!1};function ni(e,t){if(e&&e.defaultProps){for(var n in t=A({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function ri(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:A({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ai={isMounted:function(e){return!!(e=e._reactInternals)&&Ve(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),l=Ul(r,a);l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Ml(e,l,a))&&(nu(t,e,a,r),Il(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),l=Ul(r,a);l.tag=1,l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Ml(e,l,a))&&(nu(t,e,a,r),Il(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),a=Ul(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Ml(e,a,r))&&(nu(t,e,r,n),Il(t,e,r))}};function li(e,t,n,r,a,l,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,o):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(a,l))}function oi(e,t,n){var r=!1,a=Na,l=t.contextType;return"object"===typeof l&&null!==l?l=Rl(l):(a=Ta(t)?Ra:_a.current,l=(r=null!==(r=t.contextTypes)&&void 0!==r)?Oa(e,a):Na),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ai,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=l),t}function ii(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ai.enqueueReplaceState(t,t.state,null)}function si(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Dl(e);var l=t.contextType;"object"===typeof l&&null!==l?a.context=Rl(l):(l=Ta(t)?Ra:_a.current,a.context=Oa(e,l)),a.state=e.memoizedState,"function"===typeof(l=t.getDerivedStateFromProps)&&(ri(e,t,l,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&ai.enqueueReplaceState(a,a.state,null),Vl(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function ui(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(l){a="\nError generating stack: "+l.message+"\n"+l.stack}return{value:e,source:t,stack:a,digest:null}}function ci(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function di(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var fi="function"===typeof WeakMap?WeakMap:Map;function pi(e,t,n){(n=Ul(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){$s||($s=!0,Hs=r),di(0,t)},n}function hi(e,t,n){(n=Ul(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){di(0,t)}}var l=e.stateNode;return null!==l&&"function"===typeof l.componentDidCatch&&(n.callback=function(){di(0,t),"function"!==typeof r&&(null===qs?qs=new Set([this]):qs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function mi(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fi;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=ju.bind(null,e,t,n),t.then(e,e))}function vi(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function gi(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Ul(-1,1)).tag=2,Ml(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var yi=w.ReactCurrentOwner,bi=!1;function wi(e,t,n,r){t.child=null===e?xl(t,null,n,r):wl(t,e.child,n,r)}function xi(e,t,n,r,a){n=n.render;var l=t.ref;return Pl(t,a),r=vo(e,t,n,r,l,a),n=go(),null===e||bi?(al&&n&&el(t),t.flags|=1,wi(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,$i(e,t,a))}function Si(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Tu(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=zu(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,ki(e,t,l,r,a))}if(l=e.child,0===(e.lanes&a)){var o=l.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(o,r)&&e.ref===t.ref)return $i(e,t,a)}return t.flags|=1,(e=Lu(l,r)).ref=t.ref,e.return=t,t.child=e}function ki(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(sr(l,r)&&e.ref===t.ref){if(bi=!1,t.pendingProps=r=l,0===(e.lanes&a))return t.lanes=e.lanes,$i(e,t,a);0!==(131072&e.flags)&&(bi=!0)}}return Ci(e,t,n,r,a)}function Ei(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ca(Ls,Ts),Ts|=n;else{if(0===(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ca(Ls,Ts),Ts|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==l?l.baseLanes:n,Ca(Ls,Ts),Ts|=r}else null!==l?(r=l.baseLanes|n,t.memoizedState=null):r=n,Ca(Ls,Ts),Ts|=r;return wi(e,t,a,n),t.child}function ji(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ci(e,t,n,r,a){var l=Ta(n)?Ra:_a.current;return l=Oa(t,l),Pl(t,a),n=vo(e,t,n,r,l,a),r=go(),null===e||bi?(al&&r&&el(t),t.flags|=1,wi(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,$i(e,t,a))}function Ni(e,t,n,r,a){if(Ta(n)){var l=!0;Da(t)}else l=!1;if(Pl(t,a),null===t.stateNode)Wi(e,t),oi(t,n,r),si(t,n,r,a),r=!0;else if(null===e){var o=t.stateNode,i=t.memoizedProps;o.props=i;var s=o.context,u=n.contextType;"object"===typeof u&&null!==u?u=Rl(u):u=Oa(t,u=Ta(n)?Ra:_a.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof o.getSnapshotBeforeUpdate;d||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==r||s!==u)&&ii(t,o,r,u),Fl=!1;var f=t.memoizedState;o.state=f,Vl(t,r,o,a),s=t.memoizedState,i!==r||f!==s||Pa.current||Fl?("function"===typeof c&&(ri(t,n,c,r),s=t.memoizedState),(i=Fl||li(t,n,i,r,f,s,u))?(d||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),o.props=r,o.state=s,o.context=u,r=i):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Al(e,t),i=t.memoizedProps,u=t.type===t.elementType?i:ni(t.type,i),o.props=u,d=t.pendingProps,f=o.context,"object"===typeof(s=n.contextType)&&null!==s?s=Rl(s):s=Oa(t,s=Ta(n)?Ra:_a.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==d||f!==s)&&ii(t,o,r,s),Fl=!1,f=t.memoizedState,o.state=f,Vl(t,r,o,a);var h=t.memoizedState;i!==d||f!==h||Pa.current||Fl?("function"===typeof p&&(ri(t,n,p,r),h=t.memoizedState),(u=Fl||li(t,n,u,r,f,h,s)||!1)?(c||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,h,s),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,h,s)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),o.props=r,o.state=h,o.context=s,r=u):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return _i(e,t,n,r,l,a)}function _i(e,t,n,r,a,l){ji(e,t);var o=0!==(128&t.flags);if(!r&&!o)return a&&Aa(t,n,!1),$i(e,t,l);r=t.stateNode,yi.current=t;var i=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&o?(t.child=wl(t,e.child,null,l),t.child=wl(t,null,i,l)):wi(e,t,i,l),t.memoizedState=r.state,a&&Aa(t,n,!0),t.child}function Pi(e){var t=e.stateNode;t.pendingContext?za(0,t.pendingContext,t.pendingContext!==t.context):t.context&&za(0,t.context,!1),Jl(e,t.containerInfo)}function Ri(e,t,n,r,a){return pl(),hl(a),t.flags|=256,wi(e,t,n,r),t.child}var Oi,Ti,Li,zi,Fi={dehydrated:null,treeContext:null,retryLane:0};function Di(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ai(e,t,n){var r,a=t.pendingProps,o=Zl.current,i=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&o)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),Ca(Zl,1&o),null===e)return ul(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=a.children,e=a.fallback,i?(a=t.mode,i=t.child,s={mode:"hidden",children:s},0===(1&a)&&null!==i?(i.childLanes=0,i.pendingProps=s):i=Du(s,a,0,null),e=Fu(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Di(n),t.memoizedState=Fi,e):Ui(t,s));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,a,o,i){if(n)return 256&t.flags?(t.flags&=-257,Mi(e,t,i,r=ci(Error(l(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,a=t.mode,r=Du({mode:"visible",children:r.children},a,0,null),(o=Fu(o,a,i,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,0!==(1&t.mode)&&wl(t,e.child,null,i),t.child.memoizedState=Di(i),t.memoizedState=Fi,o);if(0===(1&t.mode))return Mi(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Mi(e,t,i,r=ci(o=Error(l(419)),r,void 0))}if(s=0!==(i&e.childLanes),bi||s){if(null!==(r=Ps)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==o.retryLane&&(o.retryLane=a,zl(e,a),nu(r,e,a,-1))}return mu(),Mi(e,t,i,r=ci(Error(l(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Nu.bind(null,e),a._reactRetry=t,null):(e=o.treeContext,rl=ua(a.nextSibling),nl=t,al=!0,ll=null,null!==e&&(Qa[Ka++]=Xa,Qa[Ka++]=Ya,Qa[Ka++]=Ja,Xa=e.id,Ya=e.overflow,Ja=t),t=Ui(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,o,n);if(i){i=a.fallback,s=t.mode,r=(o=e.child).sibling;var u={mode:"hidden",children:a.children};return 0===(1&s)&&t.child!==o?((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null):(a=Lu(o,u)).subtreeFlags=14680064&o.subtreeFlags,null!==r?i=Lu(r,i):(i=Fu(i,s,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,s=null===(s=e.child.memoizedState)?Di(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Fi,a}return e=(i=e.child).sibling,a=Lu(i,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Ui(e,t){return(t=Du({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Mi(e,t,n,r){return null!==r&&hl(r),wl(t,e.child,null,n),(e=Ui(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Ii(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),_l(e.return,t,n)}function Bi(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Vi(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(wi(e,t,r.children,n),0!==(2&(r=Zl.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ii(e,n,t);else if(19===e.tag)Ii(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ca(Zl,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===eo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Bi(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===eo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Bi(t,!0,n,null,l);break;case"together":Bi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wi(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function $i(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Ds|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Lu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Lu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Hi(e,t){if(!al)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function qi(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qi(e,t,n){var r=t.pendingProps;switch(tl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qi(t),null;case 1:case 17:return Ta(t.type)&&La(),qi(t),null;case 3:return r=t.stateNode,Xl(),ja(Pa),ja(_a),no(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(dl(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ll&&(ou(ll),ll=null))),Ti(e,t),qi(t),null;case 5:Gl(t);var a=Kl(Ql.current);if(n=t.type,null!==e&&null!=t.stateNode)Li(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(l(166));return qi(t),null}if(e=Kl(Hl.current),dl(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[fa]=t,r[pa]=o,e=0!==(1&t.mode),n){case"dialog":Mr("cancel",r),Mr("close",r);break;case"iframe":case"object":case"embed":Mr("load",r);break;case"video":case"audio":for(a=0;a<Fr.length;a++)Mr(Fr[a],r);break;case"source":Mr("error",r);break;case"img":case"image":case"link":Mr("error",r),Mr("load",r);break;case"details":Mr("toggle",r);break;case"input":X(r,o),Mr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Mr("invalid",r);break;case"textarea":ae(r,o),Mr("invalid",r)}for(var s in ye(n,o),a=null,o)if(o.hasOwnProperty(s)){var u=o[s];"children"===s?"string"===typeof u?r.textContent!==u&&(!0!==o.suppressHydrationWarning&&Gr(r.textContent,u,e),a=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==o.suppressHydrationWarning&&Gr(r.textContent,u,e),a=["children",""+u]):i.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&Mr("scroll",r)}switch(n){case"input":q(r),Z(r,o,!0);break;case"textarea":q(r),oe(r);break;case"select":case"option":break;default:"function"===typeof o.onClick&&(r.onclick=Zr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[fa]=t,e[pa]=r,Oi(e,t,!1,!1),t.stateNode=e;e:{switch(s=be(n,r),n){case"dialog":Mr("cancel",e),Mr("close",e),a=r;break;case"iframe":case"object":case"embed":Mr("load",e),a=r;break;case"video":case"audio":for(a=0;a<Fr.length;a++)Mr(Fr[a],e);a=r;break;case"source":Mr("error",e),a=r;break;case"img":case"image":case"link":Mr("error",e),Mr("load",e),a=r;break;case"details":Mr("toggle",e),a=r;break;case"input":X(e,r),a=J(e,r),Mr("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=A({},r,{value:void 0}),Mr("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Mr("invalid",e)}for(o in ye(n,a),u=a)if(u.hasOwnProperty(o)){var c=u[o];"style"===o?ve(e,c):"dangerouslySetInnerHTML"===o?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===o?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(i.hasOwnProperty(o)?null!=c&&"onScroll"===o&&Mr("scroll",e):null!=c&&b(e,o,c,s))}switch(n){case"input":q(e),Z(e,r,!1);break;case"textarea":q(e),oe(e);break;case"option":null!=r.value&&e.setAttribute("value",""+$(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?ne(e,!!r.multiple,o,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return qi(t),null;case 6:if(e&&null!=t.stateNode)zi(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(l(166));if(n=Kl(Ql.current),Kl(Hl.current),dl(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(o=r.nodeValue!==n)&&null!==(e=nl))switch(e.tag){case 3:Gr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Gr(r.nodeValue,n,0!==(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return qi(t),null;case 13:if(ja(Zl),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(al&&null!==rl&&0!==(1&t.mode)&&0===(128&t.flags))fl(),pl(),t.flags|=98560,o=!1;else if(o=dl(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(l(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(l(317));o[fa]=t}else pl(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;qi(t),o=!1}else null!==ll&&(ou(ll),ll=null),o=!0;if(!o)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Zl.current)?0===zs&&(zs=3):mu())),null!==t.updateQueue&&(t.flags|=4),qi(t),null);case 4:return Xl(),Ti(e,t),null===e&&Vr(t.stateNode.containerInfo),qi(t),null;case 10:return Nl(t.type._context),qi(t),null;case 19:if(ja(Zl),null===(o=t.memoizedState))return qi(t),null;if(r=0!==(128&t.flags),null===(s=o.rendering))if(r)Hi(o,!1);else{if(0!==zs||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=eo(e))){for(t.flags|=128,Hi(o,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(s=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ca(Zl,1&Zl.current|2),t.child}e=e.sibling}null!==o.tail&&Ye()>Vs&&(t.flags|=128,r=!0,Hi(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=eo(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Hi(o,!0),null===o.tail&&"hidden"===o.tailMode&&!s.alternate&&!al)return qi(t),null}else 2*Ye()-o.renderingStartTime>Vs&&1073741824!==n&&(t.flags|=128,r=!0,Hi(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=o.last)?n.sibling=s:t.child=s,o.last=s)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Ye(),t.sibling=null,n=Zl.current,Ca(Zl,r?1&n|2:1&n),t):(qi(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Ts)&&(qi(t),6&t.subtreeFlags&&(t.flags|=8192)):qi(t),null;case 24:case 25:return null}throw Error(l(156,t.tag))}function Ki(e,t){switch(tl(t),t.tag){case 1:return Ta(t.type)&&La(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Xl(),ja(Pa),ja(_a),no(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Gl(t),null;case 13:if(ja(Zl),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));pl()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return ja(Zl),null;case 4:return Xl(),null;case 10:return Nl(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Oi=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ti=function(){},Li=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Kl(Hl.current);var l,o=null;switch(n){case"input":a=J(e,a),r=J(e,r),o=[];break;case"select":a=A({},a,{value:void 0}),r=A({},r,{value:void 0}),o=[];break;case"textarea":a=re(e,a),r=re(e,r),o=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(c in ye(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var s=a[c];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(i.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(l in s)!s.hasOwnProperty(l)||u&&u.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in u)u.hasOwnProperty(l)&&s[l]!==u[l]&&(n||(n={}),n[l]=u[l])}else n||(o||(o=[]),o.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(o=o||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(o=o||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(i.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Mr("scroll",e),o||s===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}},zi=function(e,t,n,r){n!==r&&(t.flags|=4)};var Ji=!1,Xi=!1,Yi="function"===typeof WeakSet?WeakSet:Set,Gi=null;function Zi(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Eu(e,t,r)}else n.current=null}function es(e,t,n){try{n()}catch(r){Eu(e,t,r)}}var ts=!1;function ns(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var l=a.destroy;a.destroy=void 0,void 0!==l&&es(t,n,l)}a=a.next}while(a!==r)}}function rs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ls(e){var t=e.alternate;null!==t&&(e.alternate=null,ls(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ma],delete t[va],delete t[ga])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function os(e){return 5===e.tag||3===e.tag||4===e.tag}function is(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||os(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ss(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ss(e,t,n),e=e.sibling;null!==e;)ss(e,t,n),e=e.sibling}function us(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(us(e,t,n),e=e.sibling;null!==e;)us(e,t,n),e=e.sibling}var cs=null,ds=!1;function fs(e,t,n){for(n=n.child;null!==n;)ps(e,t,n),n=n.sibling}function ps(e,t,n){if(lt&&"function"===typeof lt.onCommitFiberUnmount)try{lt.onCommitFiberUnmount(at,n)}catch(i){}switch(n.tag){case 5:Xi||Zi(n,t);case 6:var r=cs,a=ds;cs=null,fs(e,t,n),ds=a,null!==(cs=r)&&(ds?(e=cs,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cs.removeChild(n.stateNode));break;case 18:null!==cs&&(ds?(e=cs,n=n.stateNode,8===e.nodeType?sa(e.parentNode,n):1===e.nodeType&&sa(e,n),Vt(e)):sa(cs,n.stateNode));break;case 4:r=cs,a=ds,cs=n.stateNode.containerInfo,ds=!0,fs(e,t,n),cs=r,ds=a;break;case 0:case 11:case 14:case 15:if(!Xi&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var l=a,o=l.destroy;l=l.tag,void 0!==o&&(0!==(2&l)||0!==(4&l))&&es(n,t,o),a=a.next}while(a!==r)}fs(e,t,n);break;case 1:if(!Xi&&(Zi(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){Eu(n,t,i)}fs(e,t,n);break;case 21:fs(e,t,n);break;case 22:1&n.mode?(Xi=(r=Xi)||null!==n.memoizedState,fs(e,t,n),Xi=r):fs(e,t,n);break;default:fs(e,t,n)}}function hs(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Yi),t.forEach((function(t){var r=_u.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function ms(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var o=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 5:cs=s.stateNode,ds=!1;break e;case 3:case 4:cs=s.stateNode.containerInfo,ds=!0;break e}s=s.return}if(null===cs)throw Error(l(160));ps(o,i,a),cs=null,ds=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(c){Eu(a,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)vs(t,e),t=t.sibling}function vs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ms(t,e),gs(e),4&r){try{ns(3,e,e.return),rs(3,e)}catch(v){Eu(e,e.return,v)}try{ns(5,e,e.return)}catch(v){Eu(e,e.return,v)}}break;case 1:ms(t,e),gs(e),512&r&&null!==n&&Zi(n,n.return);break;case 5:if(ms(t,e),gs(e),512&r&&null!==n&&Zi(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(v){Eu(e,e.return,v)}}if(4&r&&null!=(a=e.stateNode)){var o=e.memoizedProps,i=null!==n?n.memoizedProps:o,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===o.type&&null!=o.name&&Y(a,o),be(s,i);var c=be(s,o);for(i=0;i<u.length;i+=2){var d=u[i],f=u[i+1];"style"===d?ve(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,c)}switch(s){case"input":G(a,o);break;case"textarea":le(a,o);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!o.multiple;var h=o.value;null!=h?ne(a,!!o.multiple,h,!1):p!==!!o.multiple&&(null!=o.defaultValue?ne(a,!!o.multiple,o.defaultValue,!0):ne(a,!!o.multiple,o.multiple?[]:"",!1))}a[pa]=o}catch(v){Eu(e,e.return,v)}}break;case 6:if(ms(t,e),gs(e),4&r){if(null===e.stateNode)throw Error(l(162));a=e.stateNode,o=e.memoizedProps;try{a.nodeValue=o}catch(v){Eu(e,e.return,v)}}break;case 3:if(ms(t,e),gs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Vt(t.containerInfo)}catch(v){Eu(e,e.return,v)}break;case 4:default:ms(t,e),gs(e);break;case 13:ms(t,e),gs(e),8192&(a=e.child).flags&&(o=null!==a.memoizedState,a.stateNode.isHidden=o,!o||null!==a.alternate&&null!==a.alternate.memoizedState||(Bs=Ye())),4&r&&hs(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Xi=(c=Xi)||d,ms(t,e),Xi=c):ms(t,e),gs(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Gi=e,d=e.child;null!==d;){for(f=Gi=d;null!==Gi;){switch(h=(p=Gi).child,p.tag){case 0:case 11:case 14:case 15:ns(4,p,p.return);break;case 1:Zi(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(v){Eu(r,n,v)}}break;case 5:Zi(p,p.return);break;case 22:if(null!==p.memoizedState){xs(f);continue}}null!==h?(h.return=p,Gi=h):xs(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none":(s=f.stateNode,i=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,s.style.display=me("display",i))}catch(v){Eu(e,e.return,v)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(v){Eu(e,e.return,v)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ms(t,e),gs(e),4&r&&hs(e);case 21:}}function gs(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(os(n)){var r=n;break e}n=n.return}throw Error(l(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),us(e,is(e),a);break;case 3:case 4:var o=r.stateNode.containerInfo;ss(e,is(e),o);break;default:throw Error(l(161))}}catch(i){Eu(e,e.return,i)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function ys(e,t,n){Gi=e,bs(e,t,n)}function bs(e,t,n){for(var r=0!==(1&e.mode);null!==Gi;){var a=Gi,l=a.child;if(22===a.tag&&r){var o=null!==a.memoizedState||Ji;if(!o){var i=a.alternate,s=null!==i&&null!==i.memoizedState||Xi;i=Ji;var u=Xi;if(Ji=o,(Xi=s)&&!u)for(Gi=a;null!==Gi;)s=(o=Gi).child,22===o.tag&&null!==o.memoizedState?Ss(a):null!==s?(s.return=o,Gi=s):Ss(a);for(;null!==l;)Gi=l,bs(l,t,n),l=l.sibling;Gi=a,Ji=i,Xi=u}ws(e)}else 0!==(8772&a.subtreeFlags)&&null!==l?(l.return=a,Gi=l):ws(e)}}function ws(e){for(;null!==Gi;){var t=Gi;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Xi||rs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Xi)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ni(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&Wl(t,o,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Wl(t,i,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Vt(f)}}}break;default:throw Error(l(163))}Xi||512&t.flags&&as(t)}catch(p){Eu(t,t.return,p)}}if(t===e){Gi=null;break}if(null!==(n=t.sibling)){n.return=t.return,Gi=n;break}Gi=t.return}}function xs(e){for(;null!==Gi;){var t=Gi;if(t===e){Gi=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Gi=n;break}Gi=t.return}}function Ss(e){for(;null!==Gi;){var t=Gi;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rs(4,t)}catch(s){Eu(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(s){Eu(t,a,s)}}var l=t.return;try{as(t)}catch(s){Eu(t,l,s)}break;case 5:var o=t.return;try{as(t)}catch(s){Eu(t,o,s)}}}catch(s){Eu(t,t.return,s)}if(t===e){Gi=null;break}var i=t.sibling;if(null!==i){i.return=t.return,Gi=i;break}Gi=t.return}}var ks,Es=Math.ceil,js=w.ReactCurrentDispatcher,Cs=w.ReactCurrentOwner,Ns=w.ReactCurrentBatchConfig,_s=0,Ps=null,Rs=null,Os=0,Ts=0,Ls=Ea(0),zs=0,Fs=null,Ds=0,As=0,Us=0,Ms=null,Is=null,Bs=0,Vs=1/0,Ws=null,$s=!1,Hs=null,qs=null,Qs=!1,Ks=null,Js=0,Xs=0,Ys=null,Gs=-1,Zs=0;function eu(){return 0!==(6&_s)?Ye():-1!==Gs?Gs:Gs=Ye()}function tu(e){return 0===(1&e.mode)?1:0!==(2&_s)&&0!==Os?Os&-Os:null!==ml.transition?(0===Zs&&(Zs=mt()),Zs):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function nu(e,t,n,r){if(50<Xs)throw Xs=0,Ys=null,Error(l(185));gt(e,n,r),0!==(2&_s)&&e===Ps||(e===Ps&&(0===(2&_s)&&(As|=n),4===zs&&iu(e,Os)),ru(e,r),1===n&&0===_s&&0===(1&t.mode)&&(Vs=Ye()+500,Ma&&Va()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=e.pendingLanes;0<l;){var o=31-ot(l),i=1<<o,s=a[o];-1===s?0!==(i&n)&&0===(i&r)||(a[o]=pt(i,t)):s<=t&&(e.expiredLanes|=i),l&=~i}}(e,t);var r=ft(e,e===Ps?Os:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Ma=!0,Ba(e)}(su.bind(null,e)):Ba(su.bind(null,e)),oa((function(){0===(6&_s)&&Va()})),n=null;else{switch(wt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Pu(n,au.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function au(e,t){if(Gs=-1,Zs=0,0!==(6&_s))throw Error(l(327));var n=e.callbackNode;if(Su()&&e.callbackNode!==n)return null;var r=ft(e,e===Ps?Os:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=vu(e,r);else{t=r;var a=_s;_s|=2;var o=hu();for(Ps===e&&Os===t||(Ws=null,Vs=Ye()+500,fu(e,t));;)try{yu();break}catch(s){pu(e,s)}Cl(),js.current=o,_s=a,null!==Rs?t=0:(Ps=null,Os=0,t=zs)}if(0!==t){if(2===t&&(0!==(a=ht(e))&&(r=a,t=lu(e,a))),1===t)throw n=Fs,fu(e,0),iu(e,r),ru(e,Ye()),n;if(6===t)iu(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!ir(l(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=vu(e,r))&&(0!==(o=ht(e))&&(r=o,t=lu(e,o))),1===t))throw n=Fs,fu(e,0),iu(e,r),ru(e,Ye()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(l(345));case 2:case 5:xu(e,Is,Ws);break;case 3:if(iu(e,r),(130023424&r)===r&&10<(t=Bs+500-Ye())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(xu.bind(null,e,Is,Ws),t);break}xu(e,Is,Ws);break;case 4:if(iu(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-ot(r);o=1<<i,(i=t[i])>a&&(a=i),r&=~o}if(r=a,10<(r=(120>(r=Ye()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Es(r/1960))-r)){e.timeoutHandle=ra(xu.bind(null,e,Is,Ws),r);break}xu(e,Is,Ws);break;default:throw Error(l(329))}}}return ru(e,Ye()),e.callbackNode===n?au.bind(null,e):null}function lu(e,t){var n=Ms;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=vu(e,t))&&(t=Is,Is=n,null!==t&&ou(t)),e}function ou(e){null===Is?Is=e:Is.push.apply(Is,e)}function iu(e,t){for(t&=~Us,t&=~As,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function su(e){if(0!==(6&_s))throw Error(l(327));Su();var t=ft(e,0);if(0===(1&t))return ru(e,Ye()),null;var n=vu(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=lu(e,r))}if(1===n)throw n=Fs,fu(e,0),iu(e,t),ru(e,Ye()),n;if(6===n)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,xu(e,Is,Ws),ru(e,Ye()),null}function uu(e,t){var n=_s;_s|=1;try{return e(t)}finally{0===(_s=n)&&(Vs=Ye()+500,Ma&&Va())}}function cu(e){null!==Ks&&0===Ks.tag&&0===(6&_s)&&Su();var t=_s;_s|=1;var n=Ns.transition,r=bt;try{if(Ns.transition=null,bt=1,e)return e()}finally{bt=r,Ns.transition=n,0===(6&(_s=t))&&Va()}}function du(){Ts=Ls.current,ja(Ls)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Rs)for(n=Rs.return;null!==n;){var r=n;switch(tl(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&La();break;case 3:Xl(),ja(Pa),ja(_a),no();break;case 5:Gl(r);break;case 4:Xl();break;case 13:case 19:ja(Zl);break;case 10:Nl(r.type._context);break;case 22:case 23:du()}n=n.return}if(Ps=e,Rs=e=Lu(e.current,null),Os=Ts=t,zs=0,Fs=null,Us=As=Ds=0,Is=Ms=null,null!==Ol){for(t=0;t<Ol.length;t++)if(null!==(r=(n=Ol[t]).interleaved)){n.interleaved=null;var a=r.next,l=n.pending;if(null!==l){var o=l.next;l.next=a,r.next=o}n.pending=r}Ol=null}return e}function pu(e,t){for(;;){var n=Rs;try{if(Cl(),ro.current=Go,uo){for(var r=oo.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}uo=!1}if(lo=0,so=io=oo=null,co=!1,fo=0,Cs.current=null,null===n||null===n.return){zs=1,Fs=t,Rs=null;break}e:{var o=e,i=n.return,s=n,u=t;if(t=Os,s.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=vi(i);if(null!==h){h.flags&=-257,gi(h,i,s,0,t),1&h.mode&&mi(o,c,t),u=c;var m=(t=h).updateQueue;if(null===m){var v=new Set;v.add(u),t.updateQueue=v}else m.add(u);break e}if(0===(1&t)){mi(o,c,t),mu();break e}u=Error(l(426))}else if(al&&1&s.mode){var g=vi(i);if(null!==g){0===(65536&g.flags)&&(g.flags|=256),gi(g,i,s,0,t),hl(ui(u,s));break e}}o=u=ui(u,s),4!==zs&&(zs=2),null===Ms?Ms=[o]:Ms.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,Bl(o,pi(0,u,t));break e;case 1:s=u;var y=o.type,b=o.stateNode;if(0===(128&o.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===qs||!qs.has(b)))){o.flags|=65536,t&=-t,o.lanes|=t,Bl(o,hi(o,s,t));break e}}o=o.return}while(null!==o)}wu(n)}catch(w){t=w,Rs===n&&null!==n&&(Rs=n=n.return);continue}break}}function hu(){var e=js.current;return js.current=Go,null===e?Go:e}function mu(){0!==zs&&3!==zs&&2!==zs||(zs=4),null===Ps||0===(268435455&Ds)&&0===(268435455&As)||iu(Ps,Os)}function vu(e,t){var n=_s;_s|=2;var r=hu();for(Ps===e&&Os===t||(Ws=null,fu(e,t));;)try{gu();break}catch(a){pu(e,a)}if(Cl(),_s=n,js.current=r,null!==Rs)throw Error(l(261));return Ps=null,Os=0,zs}function gu(){for(;null!==Rs;)bu(Rs)}function yu(){for(;null!==Rs&&!Je();)bu(Rs)}function bu(e){var t=ks(e.alternate,e,Ts);e.memoizedProps=e.pendingProps,null===t?wu(e):Rs=t,Cs.current=null}function wu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Qi(n,t,Ts)))return void(Rs=n)}else{if(null!==(n=Ki(n,t)))return n.flags&=32767,void(Rs=n);if(null===e)return zs=6,void(Rs=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Rs=t);Rs=t=e}while(null!==t);0===zs&&(zs=5)}function xu(e,t,n){var r=bt,a=Ns.transition;try{Ns.transition=null,bt=1,function(e,t,n,r){do{Su()}while(null!==Ks);if(0!==(6&_s))throw Error(l(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-ot(n),l=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~l}}(e,o),e===Ps&&(Rs=Ps=null,Os=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Qs||(Qs=!0,Pu(tt,(function(){return Su(),null}))),o=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||o){o=Ns.transition,Ns.transition=null;var i=bt;bt=1;var s=_s;_s|=4,Cs.current=null,function(e,t){if(ea=$t,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(x){n=null;break e}var i=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==o||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=i),p===o&&++d===r&&(u=i),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},$t=!1,Gi=t;null!==Gi;)if(e=(t=Gi).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Gi=e;else for(;null!==Gi;){t=Gi;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var v=m.memoizedProps,g=m.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?v:ni(t.type,v),g);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(l(163))}}catch(x){Eu(t,t.return,x)}if(null!==(e=t.sibling)){e.return=t.return,Gi=e;break}Gi=t.return}m=ts,ts=!1}(e,n),vs(n,e),hr(ta),$t=!!ea,ta=ea=null,e.current=n,ys(n,e,a),Xe(),_s=s,bt=i,Ns.transition=o}else e.current=n;if(Qs&&(Qs=!1,Ks=e,Js=a),o=e.pendingLanes,0===o&&(qs=null),function(e){if(lt&&"function"===typeof lt.onCommitFiberRoot)try{lt.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Ye()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if($s)throw $s=!1,e=Hs,Hs=null,e;0!==(1&Js)&&0!==e.tag&&Su(),o=e.pendingLanes,0!==(1&o)?e===Ys?Xs++:(Xs=0,Ys=e):Xs=0,Va()}(e,t,n,r)}finally{Ns.transition=a,bt=r}return null}function Su(){if(null!==Ks){var e=wt(Js),t=Ns.transition,n=bt;try{if(Ns.transition=null,bt=16>e?16:e,null===Ks)var r=!1;else{if(e=Ks,Ks=null,Js=0,0!==(6&_s))throw Error(l(331));var a=_s;for(_s|=4,Gi=e.current;null!==Gi;){var o=Gi,i=o.child;if(0!==(16&Gi.flags)){var s=o.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(Gi=c;null!==Gi;){var d=Gi;switch(d.tag){case 0:case 11:case 15:ns(8,d,o)}var f=d.child;if(null!==f)f.return=d,Gi=f;else for(;null!==Gi;){var p=(d=Gi).sibling,h=d.return;if(ls(d),d===c){Gi=null;break}if(null!==p){p.return=h,Gi=p;break}Gi=h}}}var m=o.alternate;if(null!==m){var v=m.child;if(null!==v){m.child=null;do{var g=v.sibling;v.sibling=null,v=g}while(null!==v)}}Gi=o}}if(0!==(2064&o.subtreeFlags)&&null!==i)i.return=o,Gi=i;else e:for(;null!==Gi;){if(0!==(2048&(o=Gi).flags))switch(o.tag){case 0:case 11:case 15:ns(9,o,o.return)}var y=o.sibling;if(null!==y){y.return=o.return,Gi=y;break e}Gi=o.return}}var b=e.current;for(Gi=b;null!==Gi;){var w=(i=Gi).child;if(0!==(2064&i.subtreeFlags)&&null!==w)w.return=i,Gi=w;else e:for(i=b;null!==Gi;){if(0!==(2048&(s=Gi).flags))try{switch(s.tag){case 0:case 11:case 15:rs(9,s)}}catch(S){Eu(s,s.return,S)}if(s===i){Gi=null;break e}var x=s.sibling;if(null!==x){x.return=s.return,Gi=x;break e}Gi=s.return}}if(_s=a,Va(),lt&&"function"===typeof lt.onPostCommitFiberRoot)try{lt.onPostCommitFiberRoot(at,e)}catch(S){}r=!0}return r}finally{bt=n,Ns.transition=t}}return!1}function ku(e,t,n){e=Ml(e,t=pi(0,t=ui(n,t),1),1),t=eu(),null!==e&&(gt(e,1,t),ru(e,t))}function Eu(e,t,n){if(3===e.tag)ku(e,e,n);else for(;null!==t;){if(3===t.tag){ku(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===qs||!qs.has(r))){t=Ml(t,e=hi(t,e=ui(n,e),1),1),e=eu(),null!==t&&(gt(t,1,e),ru(t,e));break}}t=t.return}}function ju(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Ps===e&&(Os&n)===n&&(4===zs||3===zs&&(130023424&Os)===Os&&500>Ye()-Bs?fu(e,0):Us|=n),ru(e,t)}function Cu(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=zl(e,t))&&(gt(e,t,n),ru(e,n))}function Nu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Cu(e,n)}function _u(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(l(314))}null!==r&&r.delete(t),Cu(e,n)}function Pu(e,t){return Qe(e,t)}function Ru(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ou(e,t,n,r){return new Ru(e,t,n,r)}function Tu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Lu(e,t){var n=e.alternate;return null===n?((n=Ou(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function zu(e,t,n,r,a,o){var i=2;if(r=e,"function"===typeof e)Tu(e)&&(i=1);else if("string"===typeof e)i=5;else e:switch(e){case k:return Fu(n.children,a,o,t);case E:i=8,a|=8;break;case j:return(e=Ou(12,n,t,2|a)).elementType=j,e.lanes=o,e;case P:return(e=Ou(13,n,t,a)).elementType=P,e.lanes=o,e;case R:return(e=Ou(19,n,t,a)).elementType=R,e.lanes=o,e;case L:return Du(n,a,o,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case C:i=10;break e;case N:i=9;break e;case _:i=11;break e;case O:i=14;break e;case T:i=16,r=null;break e}throw Error(l(130,null==e?e:typeof e,""))}return(t=Ou(i,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Fu(e,t,n,r){return(e=Ou(7,e,r,t)).lanes=n,e}function Du(e,t,n,r){return(e=Ou(22,e,r,t)).elementType=L,e.lanes=n,e.stateNode={isHidden:!1},e}function Au(e,t,n){return(e=Ou(6,e,null,t)).lanes=n,e}function Uu(e,t,n){return(t=Ou(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Mu(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=vt(0),this.expirationTimes=vt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Iu(e,t,n,r,a,l,o,i,s){return e=new Mu(e,t,n,i,s),1===t?(t=1,!0===l&&(t|=8)):t=0,l=Ou(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Dl(l),e}function Bu(e){if(!e)return Na;e:{if(Ve(e=e._reactInternals)!==e||1!==e.tag)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ta(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(l(171))}if(1===e.tag){var n=e.type;if(Ta(n))return Fa(e,n,t)}return t}function Vu(e,t,n,r,a,l,o,i,s){return(e=Iu(n,r,!0,e,0,l,0,i,s)).context=Bu(null),n=e.current,(l=Ul(r=eu(),a=tu(n))).callback=void 0!==t&&null!==t?t:null,Ml(n,l,a),e.current.lanes=a,gt(e,a,r),ru(e,r),e}function Wu(e,t,n,r){var a=t.current,l=eu(),o=tu(a);return n=Bu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Ul(l,o)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Ml(a,t,o))&&(nu(e,a,o,l),Il(e,a,o)),o}function $u(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Hu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function qu(e,t){Hu(e,t),(e=e.alternate)&&Hu(e,t)}ks=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Pa.current)bi=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bi=!1,function(e,t,n){switch(t.tag){case 3:Pi(t),pl();break;case 5:Yl(t);break;case 1:Ta(t.type)&&Da(t);break;case 4:Jl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ca(Sl,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ca(Zl,1&Zl.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ai(e,t,n):(Ca(Zl,1&Zl.current),null!==(e=$i(e,t,n))?e.sibling:null);Ca(Zl,1&Zl.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Vi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ca(Zl,Zl.current),r)break;return null;case 22:case 23:return t.lanes=0,Ei(e,t,n)}return $i(e,t,n)}(e,t,n);bi=0!==(131072&e.flags)}else bi=!1,al&&0!==(1048576&t.flags)&&Za(t,qa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Wi(e,t),e=t.pendingProps;var a=Oa(t,_a.current);Pl(t,n),a=vo(null,t,r,e,a,n);var o=go();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ta(r)?(o=!0,Da(t)):o=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Dl(t),a.updater=ai,t.stateNode=a,a._reactInternals=t,si(t,r,e,n),t=_i(null,t,r,!0,o,n)):(t.tag=0,al&&o&&el(t),wi(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Wi(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Tu(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===_)return 11;if(e===O)return 14}return 2}(r),e=ni(r,e),a){case 0:t=Ci(null,t,r,e,n);break e;case 1:t=Ni(null,t,r,e,n);break e;case 11:t=xi(null,t,r,e,n);break e;case 14:t=Si(null,t,r,ni(r.type,e),n);break e}throw Error(l(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Ci(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 1:return r=t.type,a=t.pendingProps,Ni(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 3:e:{if(Pi(t),null===e)throw Error(l(387));r=t.pendingProps,a=(o=t.memoizedState).element,Al(e,t),Vl(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Ri(e,t,r,n,a=ui(Error(l(423)),t));break e}if(r!==a){t=Ri(e,t,r,n,a=ui(Error(l(424)),t));break e}for(rl=ua(t.stateNode.containerInfo.firstChild),nl=t,al=!0,ll=null,n=xl(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pl(),r===a){t=$i(e,t,n);break e}wi(e,t,r,n)}t=t.child}return t;case 5:return Yl(t),null===e&&ul(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,i=a.children,na(r,a)?i=null:null!==o&&na(r,o)&&(t.flags|=32),ji(e,t),wi(e,t,i,n),t.child;case 6:return null===e&&ul(t),null;case 13:return Ai(e,t,n);case 4:return Jl(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=wl(t,null,r,n):wi(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,xi(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 7:return wi(e,t,t.pendingProps,n),t.child;case 8:case 12:return wi(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,o=t.memoizedProps,i=a.value,Ca(Sl,r._currentValue),r._currentValue=i,null!==o)if(ir(o.value,i)){if(o.children===a.children&&!Pa.current){t=$i(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var s=o.dependencies;if(null!==s){i=o.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===o.tag){(u=Ul(-1,n&-n)).tag=2;var c=o.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}o.lanes|=n,null!==(u=o.alternate)&&(u.lanes|=n),_l(o.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===o.tag)i=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(i=o.return))throw Error(l(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),_l(i,n,t),i=o.sibling}else i=o.child;if(null!==i)i.return=o;else for(i=o;null!==i;){if(i===t){i=null;break}if(null!==(o=i.sibling)){o.return=i.return,i=o;break}i=i.return}o=i}wi(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Pl(t,n),r=r(a=Rl(a)),t.flags|=1,wi(e,t,r,n),t.child;case 14:return a=ni(r=t.type,t.pendingProps),Si(e,t,r,a=ni(r.type,a),n);case 15:return ki(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ni(r,a),Wi(e,t),t.tag=1,Ta(r)?(e=!0,Da(t)):e=!1,Pl(t,n),oi(t,r,a),si(t,r,a,n),_i(null,t,r,!0,e,n);case 19:return Vi(e,t,n);case 22:return Ei(e,t,n)}throw Error(l(156,t.tag))};var Qu="function"===typeof reportError?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}function Ju(e){this._internalRoot=e}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Yu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Gu(){}function Zu(e,t,n,r,a){var l=n._reactRootContainer;if(l){var o=l;if("function"===typeof a){var i=a;a=function(){var e=$u(o);i.call(e)}}Wu(t,o,e,a)}else o=function(e,t,n,r,a){if(a){if("function"===typeof r){var l=r;r=function(){var e=$u(o);l.call(e)}}var o=Vu(t,r,e,0,null,!1,0,"",Gu);return e._reactRootContainer=o,e[ha]=o.current,Vr(8===e.nodeType?e.parentNode:e),cu(),o}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var i=r;r=function(){var e=$u(s);i.call(e)}}var s=Iu(e,0,!1,null,0,!1,0,"",Gu);return e._reactRootContainer=s,e[ha]=s.current,Vr(8===e.nodeType?e.parentNode:e),cu((function(){Wu(t,s,n,r)})),s}(n,t,e,a,r);return $u(o)}Ju.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Wu(e,t,null,null)},Ju.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu((function(){Wu(null,e,null,null)})),t[ha]=null}},Ju.prototype.unstable_scheduleHydration=function(e){if(e){var t=Et();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&0!==t&&t<Lt[n].priority;n++);Lt.splice(n,0,e),0===n&&At(e)}},xt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),ru(t,Ye()),0===(6&_s)&&(Vs=Ye()+500,Va()))}break;case 13:cu((function(){var t=zl(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}})),qu(e,1)}},St=function(e){if(13===e.tag){var t=zl(e,134217728);if(null!==t)nu(t,e,134217728,eu());qu(e,134217728)}},kt=function(e){if(13===e.tag){var t=tu(e),n=zl(e,t);if(null!==n)nu(n,e,t,eu());qu(e,t)}},Et=function(){return bt},jt=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(G(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=xa(r);if(!a)throw Error(l(90));Q(r),G(r,a)}}}break;case"textarea":le(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},_e=uu,Pe=cu;var ec={usingClientEntryPoint:!1,Events:[ba,wa,xa,Ce,Ne,uu]},tc={findFiberByHostInstance:ya,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=He(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{at=rc.inject(nc),lt=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Xu(t))throw Error(l(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Xu(e))throw Error(l(299));var n=!1,r="",a=Qu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Iu(e,1,!1,null,0,n,0,r,a),e[ha]=t.current,Vr(8===e.nodeType?e.parentNode:e),new Ku(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=null===(e=He(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Yu(t))throw Error(l(200));return Zu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Xu(e))throw Error(l(405));var r=null!=n&&n.hydratedSources||null,a=!1,o="",i=Qu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Vu(t,null,e,1,null!=n?n:null,a,0,o,i),e[ha]=t.current,Vr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Ju(t)},t.render=function(e,t,n){if(!Yu(t))throw Error(l(200));return Zu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Yu(e))throw Error(l(40));return!!e._reactRootContainer&&(cu((function(){Zu(null,null,e,!1,(function(){e._reactRootContainer=null,e[ha]=null}))})),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Yu(n))throw Error(l(200));if(null==e||void 0===e._reactInternals)throw Error(l(38));return Zu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var l=Object.create(null);n.r(l);var o={};e=e||[null,t({}),t([]),t(t)];for(var i=2&a&&r;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach((e=>o[e]=()=>r[e]));return o.default=()=>r,n.d(l,o),l}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>Bt,hasStandardBrowserEnv:()=>Wt,hasStandardBrowserWebWorkerEnv:()=>$t,navigator:()=>Vt,origin:()=>Ht});var a,l=n(43),o=n.t(l,2),i=n(391);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},s.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(a||(a={}));const u="popstate";function c(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function d(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function f(e,t){return{usr:e.state,key:e.key,idx:t}}function p(e,t,n,r){return void 0===n&&(n=null),s({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?m(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function h(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function m(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function v(e,t,n,r){void 0===r&&(r={});let{window:l=document.defaultView,v5Compat:o=!1}=r,i=l.history,d=a.Pop,m=null,v=g();function g(){return(i.state||{idx:null}).idx}function y(){d=a.Pop;let e=g(),t=null==e?null:e-v;v=e,m&&m({action:d,location:w.location,delta:t})}function b(e){let t="null"!==l.location.origin?l.location.origin:l.location.href,n="string"===typeof e?e:h(e);return n=n.replace(/ $/,"%20"),c(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==v&&(v=0,i.replaceState(s({},i.state,{idx:v}),""));let w={get action(){return d},get location(){return e(l,i)},listen(e){if(m)throw new Error("A history only accepts one active listener");return l.addEventListener(u,y),m=e,()=>{l.removeEventListener(u,y),m=null}},createHref:e=>t(l,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){d=a.Push;let r=p(w.location,e,t);n&&n(r,e),v=g()+1;let s=f(r,v),u=w.createHref(r);try{i.pushState(s,"",u)}catch(c){if(c instanceof DOMException&&"DataCloneError"===c.name)throw c;l.location.assign(u)}o&&m&&m({action:d,location:w.location,delta:1})},replace:function(e,t){d=a.Replace;let r=p(w.location,e,t);n&&n(r,e),v=g();let l=f(r,v),s=w.createHref(r);i.replaceState(l,"",s),o&&m&&m({action:d,location:w.location,delta:0})},go:e=>i.go(e)};return w}var g;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(g||(g={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function y(e,t,n){return void 0===n&&(n="/"),b(e,t,n,!1)}function b(e,t,n,r){let a=L(("string"===typeof t?m(t):t).pathname||"/",n);if(null==a)return null;let l=w(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(l);let o=null;for(let i=0;null==o&&i<l.length;++i){let e=T(a);o=R(l[i],e,r)}return o}function w(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,l)=>{let o={relativePath:void 0===l?e.path||"":l,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};o.relativePath.startsWith("/")&&(c(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),o.relativePath=o.relativePath.slice(r.length));let i=U([r,o.relativePath]),s=n.concat(o);e.children&&e.children.length>0&&(c(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),w(e.children,t,s,i)),(null!=e.path||e.index)&&t.push({path:i,score:P(i,e.index),routesMeta:s})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of x(e.path))a(e,t,r);else a(e,t)})),t}function x(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===r.length)return a?[l,""]:[l];let o=x(r.join("/")),i=[];return i.push(...o.map((e=>""===e?l:[l,e].join("/")))),a&&i.push(...o),i.map((t=>e.startsWith("/")&&""===t?"/":t))}const S=/^:[\w-]+$/,k=3,E=2,j=1,C=10,N=-2,_=e=>"*"===e;function P(e,t){let n=e.split("/"),r=n.length;return n.some(_)&&(r+=N),t&&(r+=E),n.filter((e=>!_(e))).reduce(((e,t)=>e+(S.test(t)?k:""===t?j:C)),r)}function R(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},l="/",o=[];for(let i=0;i<r.length;++i){let e=r[i],s=i===r.length-1,u="/"===l?t:t.slice(l.length)||"/",c=O({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),d=e.route;if(!c&&s&&n&&!r[r.length-1].route.index&&(c=O({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),o.push({params:a,pathname:U([l,c.pathname]),pathnameBase:M(U([l,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(l=U([l,c.pathnameBase]))}return o}function O(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);d("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let l=new RegExp(a,t?void 0:"i");return[l,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],o=l.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";o=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const s=i[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{}),pathname:l,pathnameBase:o,pattern:e}}function T(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return d(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function L(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function z(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function F(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function D(e,t){let n=F(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function A(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=m(e):(a=s({},e),c(!a.pathname||!a.pathname.includes("?"),z("?","pathname","search",a)),c(!a.pathname||!a.pathname.includes("#"),z("#","pathname","hash",a)),c(!a.search||!a.search.includes("#"),z("#","search","hash",a)));let l,o=""===e||""===a.pathname,i=o?"/":a.pathname;if(null==i)l=n;else{let e=t.length-1;if(!r&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}l=e>=0?t[e]:"/"}let u=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?m(e):e,l=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:l,search:I(r),hash:B(a)}}(a,l),d=i&&"/"!==i&&i.endsWith("/"),f=(o||"."===i)&&n.endsWith("/");return u.pathname.endsWith("/")||!d&&!f||(u.pathname+="/"),u}const U=e=>e.join("/").replace(/\/\/+/g,"/"),M=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),I=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",B=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function V(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const W=["post","put","patch","delete"],$=(new Set(W),["get",...W]);new Set($),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function H(){return H=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},H.apply(this,arguments)}const q=l.createContext(null);const Q=l.createContext(null);const K=l.createContext(null);const J=l.createContext(null);const X=l.createContext({outlet:null,matches:[],isDataRoute:!1});const Y=l.createContext(null);function G(){return null!=l.useContext(J)}function Z(){return G()||c(!1),l.useContext(J).location}function ee(e){l.useContext(K).static||l.useLayoutEffect(e)}function te(){let{isDataRoute:e}=l.useContext(X);return e?function(){let{router:e}=de(ue.UseNavigateStable),t=pe(ce.UseNavigateStable),n=l.useRef(!1);return ee((()=>{n.current=!0})),l.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,H({fromRouteId:t},a)))}),[e,t])}():function(){G()||c(!1);let e=l.useContext(q),{basename:t,future:n,navigator:r}=l.useContext(K),{matches:a}=l.useContext(X),{pathname:o}=Z(),i=JSON.stringify(D(a,n.v7_relativeSplatPath)),s=l.useRef(!1);return ee((()=>{s.current=!0})),l.useCallback((function(n,a){if(void 0===a&&(a={}),!s.current)return;if("number"===typeof n)return void r.go(n);let l=A(n,JSON.parse(i),o,"path"===a.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:U([t,l.pathname])),(a.replace?r.replace:r.push)(l,a.state,a)}),[t,r,i,o,e])}()}function ne(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=l.useContext(K),{matches:a}=l.useContext(X),{pathname:o}=Z(),i=JSON.stringify(D(a,r.v7_relativeSplatPath));return l.useMemo((()=>A(e,JSON.parse(i),o,"path"===n)),[e,i,o,n])}function re(e,t,n,r){G()||c(!1);let{navigator:o}=l.useContext(K),{matches:i}=l.useContext(X),s=i[i.length-1],u=s?s.params:{},d=(s&&s.pathname,s?s.pathnameBase:"/");s&&s.route;let f,p=Z();if(t){var h;let e="string"===typeof t?m(t):t;"/"===d||(null==(h=e.pathname)?void 0:h.startsWith(d))||c(!1),f=e}else f=p;let v=f.pathname||"/",g=v;if("/"!==d){let e=d.replace(/^\//,"").split("/");g="/"+v.replace(/^\//,"").split("/").slice(e.length).join("/")}let b=y(e,{pathname:g});let w=se(b&&b.map((e=>Object.assign({},e,{params:Object.assign({},u,e.params),pathname:U([d,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:U([d,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),i,n,r);return t&&w?l.createElement(J.Provider,{value:{location:H({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:a.Pop}},w):w}function ae(){let e=function(){var e;let t=l.useContext(Y),n=fe(ce.UseRouteError),r=pe(ce.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=V(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:r};return l.createElement(l.Fragment,null,l.createElement("h2",null,"Unexpected Application Error!"),l.createElement("h3",{style:{fontStyle:"italic"}},t),n?l.createElement("pre",{style:a},n):null,null)}const le=l.createElement(ae,null);class oe extends l.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?l.createElement(X.Provider,{value:this.props.routeContext},l.createElement(Y.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ie(e){let{routeContext:t,match:n,children:r}=e,a=l.useContext(q);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),l.createElement(X.Provider,{value:t},r)}function se(e,t,n,r){var a;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var o;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(o=r)&&o.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,s=null==(a=n)?void 0:a.errors;if(null!=s){let e=i.findIndex((e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id])));e>=0||c(!1),i=i.slice(0,Math.min(i.length,e+1))}let u=!1,d=-1;if(n&&r&&r.v7_partialHydration)for(let l=0;l<i.length;l++){let e=i[l];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(d=l),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){u=!0,i=d>=0?i.slice(0,d+1):[i[0]];break}}}return i.reduceRight(((e,r,a)=>{let o,c=!1,f=null,p=null;var h;n&&(o=s&&r.route.id?s[r.route.id]:void 0,f=r.route.errorElement||le,u&&(d<0&&0===a?(h="route-fallback",!1||he[h]||(he[h]=!0),c=!0,p=null):d===a&&(c=!0,p=r.route.hydrateFallbackElement||null)));let m=t.concat(i.slice(0,a+1)),v=()=>{let t;return t=o?f:c?p:r.route.Component?l.createElement(r.route.Component,null):r.route.element?r.route.element:e,l.createElement(ie,{match:r,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===a)?l.createElement(oe,{location:n.location,revalidation:n.revalidation,component:f,error:o,children:v(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):v()}),null)}var ue=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ue||{}),ce=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ce||{});function de(e){let t=l.useContext(q);return t||c(!1),t}function fe(e){let t=l.useContext(Q);return t||c(!1),t}function pe(e){let t=function(){let e=l.useContext(X);return e||c(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||c(!1),n.route.id}const he={};function me(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}o.startTransition;function ve(e){c(!1)}function ge(e){let{basename:t="/",children:n=null,location:r,navigationType:o=a.Pop,navigator:i,static:s=!1,future:u}=e;G()&&c(!1);let d=t.replace(/^\/*/,"/"),f=l.useMemo((()=>({basename:d,navigator:i,static:s,future:H({v7_relativeSplatPath:!1},u)})),[d,u,i,s]);"string"===typeof r&&(r=m(r));let{pathname:p="/",search:h="",hash:v="",state:g=null,key:y="default"}=r,b=l.useMemo((()=>{let e=L(p,d);return null==e?null:{location:{pathname:e,search:h,hash:v,state:g,key:y},navigationType:o}}),[d,p,h,v,g,y,o]);return null==b?null:l.createElement(K.Provider,{value:f},l.createElement(J.Provider,{children:n,value:b}))}function ye(e){let{children:t,location:n}=e;return re(be(t),n)}new Promise((()=>{}));l.Component;function be(e,t){void 0===t&&(t=[]);let n=[];return l.Children.forEach(e,((e,r)=>{if(!l.isValidElement(e))return;let a=[...t,r];if(e.type===l.Fragment)return void n.push.apply(n,be(e.props.children,a));e.type!==ve&&c(!1),e.props.index&&e.props.children&&c(!1);let o={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(o.children=be(e.props.children,a)),n.push(o)})),n}var we=n(950),xe=n.t(we,2);function Se(){return Se=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Se.apply(this,arguments)}function ke(e,t){if(null==e)return{};var n,r,a={},l=Object.keys(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const Ee=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(mr){}new Map;const je=o.startTransition;xe.flushSync,o.useId;function Ce(e){let{basename:t,children:n,future:r,window:a}=e,o=l.useRef();var i;null==o.current&&(o.current=(void 0===(i={window:a,v5Compat:!0})&&(i={}),v((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return p("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:h(t)}),null,i)));let s=o.current,[u,c]=l.useState({action:s.action,location:s.location}),{v7_startTransition:d}=r||{},f=l.useCallback((e=>{d&&je?je((()=>c(e))):c(e)}),[c,d]);return l.useLayoutEffect((()=>s.listen(f)),[s,f]),l.useEffect((()=>me(r)),[r]),l.createElement(ge,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:s,future:r})}const Ne="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,_e=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Pe=l.forwardRef((function(e,t){let n,{onClick:r,relative:a,reloadDocument:o,replace:i,state:s,target:u,to:d,preventScrollReset:f,viewTransition:p}=e,m=ke(e,Ee),{basename:v}=l.useContext(K),g=!1;if("string"===typeof d&&_e.test(d)&&(n=d,Ne))try{let e=new URL(window.location.href),t=d.startsWith("//")?new URL(e.protocol+d):new URL(d),n=L(t.pathname,v);t.origin===e.origin&&null!=n?d=n+t.search+t.hash:g=!0}catch(mr){}let y=function(e,t){let{relative:n}=void 0===t?{}:t;G()||c(!1);let{basename:r,navigator:a}=l.useContext(K),{hash:o,pathname:i,search:s}=ne(e,{relative:n}),u=i;return"/"!==r&&(u="/"===i?r:U([r,i])),a.createHref({pathname:u,search:s,hash:o})}(d,{relative:a}),b=function(e,t){let{target:n,replace:r,state:a,preventScrollReset:o,relative:i,viewTransition:s}=void 0===t?{}:t,u=te(),c=Z(),d=ne(e,{relative:i});return l.useCallback((t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:h(c)===h(d);u(e,{replace:n,state:a,preventScrollReset:o,relative:i,viewTransition:s})}}),[c,u,d,r,a,n,e,o,i,s])}(d,{replace:i,state:s,target:u,preventScrollReset:f,relative:a,viewTransition:p});return l.createElement("a",Se({},m,{href:n||y,onClick:g||o?r:function(e){r&&r(e),e.defaultPrevented||b(e)},ref:t,target:u}))}));var Re,Oe;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Re||(Re={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(Oe||(Oe={}));function Te(e,t){return function(){return e.apply(t,arguments)}}const{toString:Le}=Object.prototype,{getPrototypeOf:ze}=Object,{iterator:Fe,toStringTag:De}=Symbol,Ae=(Ue=Object.create(null),e=>{const t=Le.call(e);return Ue[t]||(Ue[t]=t.slice(8,-1).toLowerCase())});var Ue;const Me=e=>(e=e.toLowerCase(),t=>Ae(t)===e),Ie=e=>t=>typeof t===e,{isArray:Be}=Array,Ve=Ie("undefined");const We=Me("ArrayBuffer");const $e=Ie("string"),He=Ie("function"),qe=Ie("number"),Qe=e=>null!==e&&"object"===typeof e,Ke=e=>{if("object"!==Ae(e))return!1;const t=ze(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(De in e)&&!(Fe in e)},Je=Me("Date"),Xe=Me("File"),Ye=Me("Blob"),Ge=Me("FileList"),Ze=Me("URLSearchParams"),[et,tt,nt,rt]=["ReadableStream","Request","Response","Headers"].map(Me);function at(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),Be(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),l=r.length;let o;for(n=0;n<l;n++)o=r[n],t.call(null,e[o],o,e)}}function lt(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const ot="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,it=e=>!Ve(e)&&e!==ot;const st=(ut="undefined"!==typeof Uint8Array&&ze(Uint8Array),e=>ut&&e instanceof ut);var ut;const ct=Me("HTMLFormElement"),dt=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),ft=Me("RegExp"),pt=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};at(n,((n,a)=>{let l;!1!==(l=t(n,a,e))&&(r[a]=l||n)})),Object.defineProperties(e,r)};const ht=Me("AsyncFunction"),mt=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],ot.addEventListener("message",(e=>{let{source:t,data:a}=e;t===ot&&a===n&&r.length&&r.shift()()}),!1),e=>{r.push(e),ot.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,He(ot.postMessage)),vt="undefined"!==typeof queueMicrotask?queueMicrotask.bind(ot):"undefined"!==typeof process&&process.nextTick||mt,gt={isArray:Be,isArrayBuffer:We,isBuffer:function(e){return null!==e&&!Ve(e)&&null!==e.constructor&&!Ve(e.constructor)&&He(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||He(e.append)&&("formdata"===(t=Ae(e))||"object"===t&&He(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&We(e.buffer),t},isString:$e,isNumber:qe,isBoolean:e=>!0===e||!1===e,isObject:Qe,isPlainObject:Ke,isReadableStream:et,isRequest:tt,isResponse:nt,isHeaders:rt,isUndefined:Ve,isDate:Je,isFile:Xe,isBlob:Ye,isRegExp:ft,isFunction:He,isStream:e=>Qe(e)&&He(e.pipe),isURLSearchParams:Ze,isTypedArray:st,isFileList:Ge,forEach:at,merge:function e(){const{caseless:t}=it(this)&&this||{},n={},r=(r,a)=>{const l=t&&lt(n,a)||a;Ke(n[l])&&Ke(r)?n[l]=e(n[l],r):Ke(r)?n[l]=e({},r):Be(r)?n[l]=r.slice():n[l]=r};for(let a=0,l=arguments.length;a<l;a++)arguments[a]&&at(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return at(t,((t,r)=>{n&&He(t)?e[r]=Te(t,n):e[r]=t}),{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,l,o;const i={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),l=a.length;l-- >0;)o=a[l],r&&!r(o,e,t)||i[o]||(t[o]=e[o],i[o]=!0);e=!1!==n&&ze(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Ae,kindOfTest:Me,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(Be(e))return e;let t=e.length;if(!qe(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Fe]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:ct,hasOwnProperty:dt,hasOwnProp:dt,reduceDescriptors:pt,freezeMethods:e=>{pt(e,((t,n)=>{if(He(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];He(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return Be(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:lt,global:ot,isContextDefined:it,isSpecCompliantForm:function(e){return!!(e&&He(e.append)&&"FormData"===e[De]&&e[Fe])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(Qe(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=Be(e)?[]:{};return at(e,((e,t)=>{const l=n(e,r+1);!Ve(l)&&(a[t]=l)})),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:ht,isThenable:e=>e&&(Qe(e)||He(e))&&He(e.then)&&He(e.catch),setImmediate:mt,asap:vt,isIterable:e=>null!=e&&He(e[Fe])};function yt(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}gt.inherits(yt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:gt.toJSONObject(this.config),code:this.code,status:this.status}}});const bt=yt.prototype,wt={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{wt[e]={value:e}})),Object.defineProperties(yt,wt),Object.defineProperty(bt,"isAxiosError",{value:!0}),yt.from=(e,t,n,r,a,l)=>{const o=Object.create(bt);return gt.toFlatObject(e,o,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),yt.call(o,e.message,t,n,r,a),o.cause=e,o.name=e.name,l&&Object.assign(o,l),o};const xt=yt;function St(e){return gt.isPlainObject(e)||gt.isArray(e)}function kt(e){return gt.endsWith(e,"[]")?e.slice(0,-2):e}function Et(e,t,n){return e?e.concat(t).map((function(e,t){return e=kt(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const jt=gt.toFlatObject(gt,{},null,(function(e){return/^is[A-Z]/.test(e)}));const Ct=function(e,t,n){if(!gt.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=gt.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!gt.isUndefined(t[e])}))).metaTokens,a=n.visitor||u,l=n.dots,o=n.indexes,i=(n.Blob||"undefined"!==typeof Blob&&Blob)&&gt.isSpecCompliantForm(t);if(!gt.isFunction(a))throw new TypeError("visitor must be a function");function s(e){if(null===e)return"";if(gt.isDate(e))return e.toISOString();if(gt.isBoolean(e))return e.toString();if(!i&&gt.isBlob(e))throw new xt("Blob is not supported. Use a Buffer instead.");return gt.isArrayBuffer(e)||gt.isTypedArray(e)?i&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,a){let i=e;if(e&&!a&&"object"===typeof e)if(gt.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(gt.isArray(e)&&function(e){return gt.isArray(e)&&!e.some(St)}(e)||(gt.isFileList(e)||gt.endsWith(n,"[]"))&&(i=gt.toArray(e)))return n=kt(n),i.forEach((function(e,r){!gt.isUndefined(e)&&null!==e&&t.append(!0===o?Et([n],r,l):null===o?n:n+"[]",s(e))})),!1;return!!St(e)||(t.append(Et(a,n,l),s(e)),!1)}const c=[],d=Object.assign(jt,{defaultVisitor:u,convertValue:s,isVisitable:St});if(!gt.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!gt.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),gt.forEach(n,(function(n,l){!0===(!(gt.isUndefined(n)||null===n)&&a.call(t,n,gt.isString(l)?l.trim():l,r,d))&&e(n,r?r.concat(l):[l])})),c.pop()}}(e),t};function Nt(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function _t(e,t){this._pairs=[],e&&Ct(e,this,t)}const Pt=_t.prototype;Pt.append=function(e,t){this._pairs.push([e,t])},Pt.toString=function(e){const t=e?function(t){return e.call(this,t,Nt)}:Nt;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const Rt=_t;function Ot(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Tt(e,t,n){if(!t)return e;const r=n&&n.encode||Ot;gt.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let l;if(l=a?a(t,n):gt.isURLSearchParams(t)?t.toString():new Rt(t,n).toString(r),l){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+l}return e}const Lt=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){gt.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},zt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};function Ft(e){return Ft="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ft(e)}function Dt(e){var t=function(e,t){if("object"!=Ft(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Ft(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Ft(t)?t:t+""}function At(e,t,n){return(t=Dt(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ut(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Mt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ut(Object(n),!0).forEach((function(t){At(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ut(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const It={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:Rt,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Bt="undefined"!==typeof window&&"undefined"!==typeof document,Vt="object"===typeof navigator&&navigator||void 0,Wt=Bt&&(!Vt||["ReactNative","NativeScript","NS"].indexOf(Vt.product)<0),$t="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,Ht=Bt&&window.location.href||"http://localhost",qt=Mt(Mt({},r),It);const Qt=function(e){function t(e,n,r,a){let l=e[a++];if("__proto__"===l)return!0;const o=Number.isFinite(+l),i=a>=e.length;if(l=!l&&gt.isArray(r)?r.length:l,i)return gt.hasOwnProp(r,l)?r[l]=[r[l],n]:r[l]=n,!o;r[l]&&gt.isObject(r[l])||(r[l]=[]);return t(e,n,r[l],a)&&gt.isArray(r[l])&&(r[l]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let l;for(r=0;r<a;r++)l=n[r],t[l]=e[l];return t}(r[l])),!o}if(gt.isFormData(e)&&gt.isFunction(e.entries)){const n={};return gt.forEachEntry(e,((e,r)=>{t(function(e){return gt.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null};const Kt={transitional:zt,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=gt.isObject(e);a&&gt.isHTMLForm(e)&&(e=new FormData(e));if(gt.isFormData(e))return r?JSON.stringify(Qt(e)):e;if(gt.isArrayBuffer(e)||gt.isBuffer(e)||gt.isStream(e)||gt.isFile(e)||gt.isBlob(e)||gt.isReadableStream(e))return e;if(gt.isArrayBufferView(e))return e.buffer;if(gt.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let l;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Ct(e,new qt.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return qt.isNode&&gt.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((l=gt.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Ct(l?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(gt.isString(e))try{return(t||JSON.parse)(e),gt.trim(e)}catch(mr){if("SyntaxError"!==mr.name)throw mr}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Kt.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(gt.isResponse(e)||gt.isReadableStream(e))return e;if(e&&gt.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(mr){if(n){if("SyntaxError"===mr.name)throw xt.from(mr,xt.ERR_BAD_RESPONSE,this,null,this.response);throw mr}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:qt.classes.FormData,Blob:qt.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};gt.forEach(["delete","get","head","post","put","patch"],(e=>{Kt.headers[e]={}}));const Jt=Kt,Xt=gt.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Yt=Symbol("internals");function Gt(e){return e&&String(e).trim().toLowerCase()}function Zt(e){return!1===e||null==e?e:gt.isArray(e)?e.map(Zt):String(e)}function en(e,t,n,r,a){return gt.isFunction(r)?r.call(this,t,n):(a&&(t=n),gt.isString(t)?gt.isString(r)?-1!==t.indexOf(r):gt.isRegExp(r)?r.test(t):void 0:void 0)}class tn{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=Gt(t);if(!a)throw new Error("header name must be a non-empty string");const l=gt.findKey(r,a);(!l||void 0===r[l]||!0===n||void 0===n&&!1!==r[l])&&(r[l||t]=Zt(e))}const l=(e,t)=>gt.forEach(e,((e,n)=>a(e,n,t)));if(gt.isPlainObject(e)||e instanceof this.constructor)l(e,t);else if(gt.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))l((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach((function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&Xt[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(gt.isObject(e)&&gt.isIterable(e)){let n,r,a={};for(const t of e){if(!gt.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?gt.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}l(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=Gt(e)){const n=gt.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(gt.isFunction(t))return t.call(this,e,n);if(gt.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Gt(e)){const n=gt.findKey(this,e);return!(!n||void 0===this[n]||t&&!en(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=Gt(e)){const a=gt.findKey(n,e);!a||t&&!en(0,n[a],a,t)||(delete n[a],r=!0)}}return gt.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!en(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return gt.forEach(this,((r,a)=>{const l=gt.findKey(n,a);if(l)return t[l]=Zt(r),void delete t[a];const o=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(a):String(a).trim();o!==a&&delete t[a],t[o]=Zt(r),n[o]=!0})),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return gt.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&gt.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((e=>{let[t,n]=e;return t+": "+n})).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach((e=>t.set(e))),t}static accessor(e){const t=(this[Yt]=this[Yt]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Gt(e);t[r]||(!function(e,t){const n=gt.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})}))}(n,e),t[r]=!0)}return gt.isArray(e)?e.forEach(r):r(e),this}}tn.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),gt.reduceDescriptors(tn.prototype,((e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}})),gt.freezeMethods(tn);const nn=tn;function rn(e,t){const n=this||Jt,r=t||n,a=nn.from(r.headers);let l=r.data;return gt.forEach(e,(function(e){l=e.call(n,l,a.normalize(),t?t.status:void 0)})),a.normalize(),l}function an(e){return!(!e||!e.__CANCEL__)}function ln(e,t,n){xt.call(this,null==e?"canceled":e,xt.ERR_CANCELED,t,n),this.name="CanceledError"}gt.inherits(ln,xt,{__CANCEL__:!0});const on=ln;function sn(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new xt("Request failed with status code "+n.status,[xt.ERR_BAD_REQUEST,xt.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const un=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,l=0,o=0;return t=void 0!==t?t:1e3,function(i){const s=Date.now(),u=r[o];a||(a=s),n[l]=i,r[l]=s;let c=o,d=0;for(;c!==l;)d+=n[c++],c%=e;if(l=(l+1)%e,l===o&&(o=(o+1)%e),s-a<t)return;const f=u&&s-u;return f?Math.round(1e3*d/f):void 0}};const cn=function(e,t){let n,r,a=0,l=1e3/t;const o=function(t){let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=l,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var i=arguments.length,s=new Array(i),u=0;u<i;u++)s[u]=arguments[u];t>=l?o(s,e):(n=s,r||(r=setTimeout((()=>{r=null,o(n)}),l-t)))},()=>n&&o(n)]},dn=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=un(50,250);return cn((n=>{const l=n.loaded,o=n.lengthComputable?n.total:void 0,i=l-r,s=a(i);r=l;e({loaded:l,total:o,progress:o?l/o:void 0,bytes:i,rate:s||void 0,estimated:s&&o&&l<=o?(o-l)/s:void 0,event:n,lengthComputable:null!=o,[t?"download":"upload"]:!0})}),n)},fn=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},pn=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return gt.asap((()=>e(...n)))},hn=qt.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,qt.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(qt.origin),qt.navigator&&/(msie|trident)/i.test(qt.navigator.userAgent)):()=>!0,mn=qt.hasStandardBrowserEnv?{write(e,t,n,r,a,l){const o=[e+"="+encodeURIComponent(t)];gt.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),gt.isString(r)&&o.push("path="+r),gt.isString(a)&&o.push("domain="+a),!0===l&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function vn(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const gn=e=>e instanceof nn?Mt({},e):e;function yn(e,t){t=t||{};const n={};function r(e,t,n,r){return gt.isPlainObject(e)&&gt.isPlainObject(t)?gt.merge.call({caseless:r},e,t):gt.isPlainObject(t)?gt.merge({},t):gt.isArray(t)?t.slice():t}function a(e,t,n,a){return gt.isUndefined(t)?gt.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function l(e,t){if(!gt.isUndefined(t))return r(void 0,t)}function o(e,t){return gt.isUndefined(t)?gt.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function i(n,a,l){return l in t?r(n,a):l in e?r(void 0,n):void 0}const s={url:l,method:l,data:l,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:i,headers:(e,t,n)=>a(gn(e),gn(t),0,!0)};return gt.forEach(Object.keys(Object.assign({},e,t)),(function(r){const l=s[r]||a,o=l(e[r],t[r],r);gt.isUndefined(o)&&l!==i||(n[r]=o)})),n}const bn=e=>{const t=yn({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:l,xsrfCookieName:o,headers:i,auth:s}=t;if(t.headers=i=nn.from(i),t.url=Tt(vn(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&i.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),gt.isFormData(r))if(qt.hasStandardBrowserEnv||qt.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(n=i.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(qt.hasStandardBrowserEnv&&(a&&gt.isFunction(a)&&(a=a(t)),a||!1!==a&&hn(t.url))){const e=l&&o&&mn.read(o);e&&i.set(l,e)}return t},wn="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=bn(e);let a=r.data;const l=nn.from(r.headers).normalize();let o,i,s,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(o),r.signal&&r.signal.removeEventListener("abort",o)}let m=new XMLHttpRequest;function v(){if(!m)return;const r=nn.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());sn((function(e){t(e),h()}),(function(e){n(e),h()}),{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=v:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(v)},m.onabort=function(){m&&(n(new xt("Request aborted",xt.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new xt("Network Error",xt.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||zt;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new xt(t,a.clarifyTimeoutError?xt.ETIMEDOUT:xt.ECONNABORTED,e,m)),m=null},void 0===a&&l.setContentType(null),"setRequestHeader"in m&&gt.forEach(l.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),gt.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([s,c]=dn(p,!0),m.addEventListener("progress",s)),f&&m.upload&&([i,u]=dn(f),m.upload.addEventListener("progress",i),m.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(o=t=>{m&&(n(!t||t.type?new on(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(o),r.signal&&(r.signal.aborted?o():r.signal.addEventListener("abort",o)));const g=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);g&&-1===qt.protocols.indexOf(g)?n(new xt("Unsupported protocol "+g+":",xt.ERR_BAD_REQUEST,e)):m.send(a||null)}))},xn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,o();const t=e instanceof Error?e:this.reason;r.abort(t instanceof xt?t:new on(t instanceof Error?t.message:t))}};let l=t&&setTimeout((()=>{l=null,a(new xt("timeout ".concat(t," of ms exceeded"),xt.ETIMEDOUT))}),t);const o=()=>{e&&(l&&clearTimeout(l),l=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)})),e=null)};e.forEach((e=>e.addEventListener("abort",a)));const{signal:i}=r;return i.unsubscribe=()=>gt.asap(o),i}};function Sn(e,t){this.v=e,this.k=t}function kn(e){return function(){return new En(e.apply(this,arguments))}}function En(e){var t,n;function r(t,n){try{var l=e[t](n),o=l.value,i=o instanceof Sn;Promise.resolve(i?o.v:o).then((function(n){if(i){var s="return"===t?"return":"next";if(!o.k||n.done)return r(s,n);n=e[s](n).value}a(l.done?"return":"normal",n)}),(function(e){r("throw",e)}))}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise((function(l,o){var i={key:e,arg:a,resolve:l,reject:o,next:null};n?n=n.next=i:(t=n=i,r(e,a))}))},"function"!=typeof e.return&&(this.return=void 0)}function jn(e){return new Sn(e,0)}function Cn(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise((function(n){n(e[t](r))})),{done:!1,value:new Sn(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function Nn(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new _n(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function _n(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then((function(e){return{value:e,done:t}}))}return _n=function(e){this.s=e,this.n=e.next},_n.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new _n(e)}En.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},En.prototype.next=function(e){return this._invoke("next",e)},En.prototype.throw=function(e){return this._invoke("throw",e)},En.prototype.return=function(e){return this._invoke("return",e)};const Pn=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},Rn=function(){var e=kn((function*(e,t){var n,r=!1,a=!1;try{for(var l,o=Nn(On(e));r=!(l=yield jn(o.next())).done;r=!1){const e=l.value;yield*Cn(Nn(Pn(e,t)))}}catch(i){a=!0,n=i}finally{try{r&&null!=o.return&&(yield jn(o.return()))}finally{if(a)throw n}}}));return function(t,n){return e.apply(this,arguments)}}(),On=function(){var e=kn((function*(e){if(e[Symbol.asyncIterator])return void(yield*Cn(Nn(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield jn(t.read());if(e)break;yield n}}finally{yield jn(t.cancel())}}));return function(t){return e.apply(this,arguments)}}(),Tn=(e,t,n,r)=>{const a=Rn(e,t);let l,o=0,i=e=>{l||(l=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return i(),void e.close();let l=r.byteLength;if(n){let e=o+=l;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw i(t),t}},cancel:e=>(i(e),a.return())},{highWaterMark:2})},Ln="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,zn=Ln&&"function"===typeof ReadableStream,Fn=Ln&&("function"===typeof TextEncoder?(Dn=new TextEncoder,e=>Dn.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var Dn;const An=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(mr){return!1}},Un=zn&&An((()=>{let e=!1;const t=new Request(qt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),Mn=zn&&An((()=>gt.isReadableStream(new Response("").body))),In={stream:Mn&&(e=>e.body)};var Bn;Ln&&(Bn=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!In[e]&&(In[e]=gt.isFunction(Bn[e])?t=>t[e]():(t,n)=>{throw new xt("Response type '".concat(e,"' is not supported"),xt.ERR_NOT_SUPPORT,n)})})));const Vn=async(e,t)=>{const n=gt.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(gt.isBlob(e))return e.size;if(gt.isSpecCompliantForm(e)){const t=new Request(qt.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return gt.isArrayBufferView(e)||gt.isArrayBuffer(e)?e.byteLength:(gt.isURLSearchParams(e)&&(e+=""),gt.isString(e)?(await Fn(e)).byteLength:void 0)})(t):n},Wn=Ln&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:l,timeout:o,onDownloadProgress:i,onUploadProgress:s,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:f}=bn(e);u=u?(u+"").toLowerCase():"text";let p,h=xn([a,l&&l.toAbortSignal()],o);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let v;try{if(s&&Un&&"get"!==n&&"head"!==n&&0!==(v=await Vn(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(gt.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=fn(v,dn(pn(s)));r=Tn(n.body,65536,e,t)}}gt.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,Mt(Mt({},f),{},{signal:h,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0}));let l=await fetch(p,f);const o=Mn&&("stream"===u||"response"===u);if(Mn&&(i||o&&m)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=l[t]}));const t=gt.toFiniteNumber(l.headers.get("content-length")),[n,r]=i&&fn(t,dn(pn(i),!0))||[];l=new Response(Tn(l.body,65536,n,(()=>{r&&r(),m&&m()})),e)}u=u||"text";let g=await In[gt.findKey(In,u)||"text"](l,e);return!o&&m&&m(),await new Promise(((t,n)=>{sn(t,n,{data:g,headers:nn.from(l.headers),status:l.status,statusText:l.statusText,config:e,request:p})}))}catch(g){if(m&&m(),g&&"TypeError"===g.name&&/Load failed|fetch/i.test(g.message))throw Object.assign(new xt("Network Error",xt.ERR_NETWORK,e,p),{cause:g.cause||g});throw xt.from(g,g&&g.code,e,p)}}),$n={http:null,xhr:wn,fetch:Wn};gt.forEach($n,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(mr){}Object.defineProperty(e,"adapterName",{value:t})}}));const Hn=e=>"- ".concat(e),qn=e=>gt.isFunction(e)||null===e||!1===e,Qn=e=>{e=gt.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let l=0;l<t;l++){let t;if(n=e[l],r=n,!qn(n)&&(r=$n[(t=String(n)).toLowerCase()],void 0===r))throw new xt("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+l]=r}if(!r){const e=Object.entries(a).map((e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")}));let n=t?e.length>1?"since :\n"+e.map(Hn).join("\n"):" "+Hn(e[0]):"as no adapter specified";throw new xt("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Kn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new on(null,e)}function Jn(e){Kn(e),e.headers=nn.from(e.headers),e.data=rn.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Qn(e.adapter||Jt.adapter)(e).then((function(t){return Kn(e),t.data=rn.call(e,e.transformResponse,t),t.headers=nn.from(t.headers),t}),(function(t){return an(t)||(Kn(e),t&&t.response&&(t.response.data=rn.call(e,e.transformResponse,t.response),t.response.headers=nn.from(t.response.headers))),Promise.reject(t)}))}const Xn="1.10.0",Yn={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{Yn[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const Gn={};Yn.transitional=function(e,t,n){function r(e,t){return"[Axios v"+Xn+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,l)=>{if(!1===e)throw new xt(r(a," has been removed"+(t?" in "+t:"")),xt.ERR_DEPRECATED);return t&&!Gn[a]&&(Gn[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,l)}},Yn.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const Zn={assertOptions:function(e,t,n){if("object"!==typeof e)throw new xt("options must be an object",xt.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const l=r[a],o=t[l];if(o){const t=e[l],n=void 0===t||o(t,l,e);if(!0!==n)throw new xt("option "+l+" must be "+n,xt.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new xt("Unknown option "+l,xt.ERR_BAD_OPTION)}},validators:Yn},er=Zn.validators;class tr{constructor(e){this.defaults=e||{},this.interceptors={request:new Lt,response:new Lt}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(mr){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=yn(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&Zn.assertOptions(n,{silentJSONParsing:er.transitional(er.boolean),forcedJSONParsing:er.transitional(er.boolean),clarifyTimeoutError:er.transitional(er.boolean)},!1),null!=r&&(gt.isFunction(r)?t.paramsSerializer={serialize:r}:Zn.assertOptions(r,{encode:er.function,serialize:er.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),Zn.assertOptions(t,{baseUrl:er.spelling("baseURL"),withXsrfToken:er.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let l=a&&gt.merge(a.common,a[t.method]);a&&gt.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete a[e]})),t.headers=nn.concat(l,a);const o=[];let i=!0;this.interceptors.request.forEach((function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,o.unshift(e.fulfilled,e.rejected))}));const s=[];let u;this.interceptors.response.forEach((function(e){s.push(e.fulfilled,e.rejected)}));let c,d=0;if(!i){const e=[Jn.bind(this),void 0];for(e.unshift.apply(e,o),e.push.apply(e,s),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=o.length;let f=t;for(d=0;d<c;){const e=o[d++],t=o[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{u=Jn.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,c=s.length;d<c;)u=u.then(s[d++],s[d++]);return u}getUri(e){return Tt(vn((e=yn(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}gt.forEach(["delete","get","head","options"],(function(e){tr.prototype[e]=function(t,n){return this.request(yn(n||{},{method:e,url:t,data:(n||{}).data}))}})),gt.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,a){return this.request(yn(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}tr.prototype[e]=t(),tr.prototype[e+"Form"]=t(!0)}));const nr=tr;class rr{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,a){n.reason||(n.reason=new on(e,r,a),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new rr((function(t){e=t})),cancel:e}}}const ar=rr;const lr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(lr).forEach((e=>{let[t,n]=e;lr[n]=t}));const or=lr;const ir=function e(t){const n=new nr(t),r=Te(nr.prototype.request,n);return gt.extend(r,nr.prototype,n,{allOwnKeys:!0}),gt.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(yn(t,n))},r}(Jt);ir.Axios=nr,ir.CanceledError=on,ir.CancelToken=ar,ir.isCancel=an,ir.VERSION=Xn,ir.toFormData=Ct,ir.AxiosError=xt,ir.Cancel=ir.CanceledError,ir.all=function(e){return Promise.all(e)},ir.spread=function(e){return function(t){return e.apply(null,t)}},ir.isAxiosError=function(e){return gt.isObject(e)&&!0===e.isAxiosError},ir.mergeConfig=yn,ir.AxiosHeaders=nn,ir.formToJSON=e=>Qt(gt.isHTMLForm(e)?new FormData(e):e),ir.getAdapter=Qn,ir.HttpStatusCode=or,ir.default=ir;const sr=ir;var ur=n(579);const cr=function(){var e,t,n,r,a,o,i,s,u;const[c,d]=(0,l.useState)(null),[f,p]=(0,l.useState)([]),[h,m]=(0,l.useState)(!0),[v,g]=(0,l.useState)(null);(0,l.useEffect)((()=>{y()}),[]);const y=async()=>{try{m(!0);const e=await sr.get("/api/upload/status");d(e.data);const t=await sr.get("/api/upload/jobs?limit=5");p(t.data.jobs)}catch(e){g("Failed to load dashboard data"),console.error("Dashboard error:",e)}finally{m(!1)}},b=e=>{switch(e){case"pending":return"status-badge status-pending";case"processing":return"status-badge status-processing";case"completed":return"status-badge status-completed";case"failed":return"status-badge status-failed";default:return"status-badge"}},w=e=>{if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return h?(0,ur.jsx)("div",{className:"card",children:(0,ur.jsx)("div",{className:"loading",children:"Loading dashboard..."})}):v?(0,ur.jsx)("div",{className:"card",children:(0,ur.jsx)("div",{className:"error",children:v})}):(0,ur.jsxs)("div",{children:[(0,ur.jsxs)("div",{className:"card",children:[(0,ur.jsx)("h2",{children:"System Overview"}),c&&(0,ur.jsxs)(ur.Fragment,{children:[(0,ur.jsxs)("div",{className:"metrics-grid",children:[(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:c.uploadStats.total}),(0,ur.jsx)("div",{className:"metric-label",children:"Total Jobs"})]}),(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:(null===(e=c.uploadStats.byStatus)||void 0===e||null===(t=e.completed)||void 0===t?void 0:t.count)||0}),(0,ur.jsx)("div",{className:"metric-label",children:"Completed"})]}),(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:(null===(n=c.uploadStats.byStatus)||void 0===n||null===(r=n.processing)||void 0===r?void 0:r.count)||0}),(0,ur.jsx)("div",{className:"metric-label",children:"Processing"})]}),(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:(null===(a=c.uploadStats.byStatus)||void 0===a||null===(o=a.failed)||void 0===o?void 0:o.count)||0}),(0,ur.jsx)("div",{className:"metric-label",children:"Failed"})]})]}),c.queueStats&&(0,ur.jsxs)("div",{children:[(0,ur.jsx)("h3",{children:"Queue Status"}),(0,ur.jsxs)("div",{className:"metrics-grid",children:[(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:(null===(i=c.queueStats.csvProcessing)||void 0===i?void 0:i.waiting)||0}),(0,ur.jsx)("div",{className:"metric-label",children:"CSV Queue"})]}),(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:(null===(s=c.queueStats.analysis)||void 0===s?void 0:s.waiting)||0}),(0,ur.jsx)("div",{className:"metric-label",children:"Analysis Queue"})]}),(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:(null===(u=c.queueStats.optimization)||void 0===u?void 0:u.waiting)||0}),(0,ur.jsx)("div",{className:"metric-label",children:"Optimization Queue"})]})]})]})]})]}),(0,ur.jsxs)("div",{className:"card",children:[(0,ur.jsx)("h2",{children:"Recent Jobs"}),0===f.length?(0,ur.jsxs)("p",{children:["No jobs found. ",(0,ur.jsx)("a",{href:"/upload",children:"Upload your first CSV file"})," to get started."]}):(0,ur.jsx)("div",{className:"table-container",children:(0,ur.jsxs)("table",{className:"table",children:[(0,ur.jsx)("thead",{children:(0,ur.jsxs)("tr",{children:[(0,ur.jsx)("th",{children:"File Name"}),(0,ur.jsx)("th",{children:"Status"}),(0,ur.jsx)("th",{children:"File Size"}),(0,ur.jsx)("th",{children:"Created"}),(0,ur.jsx)("th",{children:"Progress"})]})}),(0,ur.jsx)("tbody",{children:f.map((e=>{return(0,ur.jsxs)("tr",{children:[(0,ur.jsx)("td",{children:(0,ur.jsx)("a",{href:"/results/".concat(e.id),style:{textDecoration:"none",color:"#667eea"},children:e.originalFilename})}),(0,ur.jsx)("td",{children:(0,ur.jsx)("span",{className:b(e.status),children:e.status})}),(0,ur.jsx)("td",{children:w(e.fileSize)}),(0,ur.jsx)("td",{children:(t=e.createdAt,new Date(t).toLocaleString())}),(0,ur.jsxs)("td",{children:[(0,ur.jsx)("div",{className:"progress-bar",children:(0,ur.jsx)("div",{className:"progress-fill",style:{width:"".concat(e.progress||0,"%")}})}),(0,ur.jsxs)("small",{children:[e.progress||0,"%"]})]})]},e.id);var t}))})]})})]}),(0,ur.jsxs)("div",{className:"card",children:[(0,ur.jsx)("h2",{children:"Getting Started"}),(0,ur.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"1rem"},children:[(0,ur.jsxs)("div",{style:{padding:"1rem",background:"#f8fafc",borderRadius:"8px"},children:[(0,ur.jsx)("h3",{children:"1. Upload Your Data"}),(0,ur.jsx)("p",{children:"Upload your Amazon PPC CSV file containing campaign data, keywords, and performance metrics."}),(0,ur.jsx)("a",{href:"/upload",className:"upload-button",style:{textDecoration:"none",display:"inline-block"},children:"Upload CSV File"})]}),(0,ur.jsxs)("div",{style:{padding:"1rem",background:"#f8fafc",borderRadius:"8px"},children:[(0,ur.jsx)("h3",{children:"2. AI Analysis"}),(0,ur.jsx)("p",{children:"Our AI agents will analyze your data, identify patterns, and generate actionable insights."})]}),(0,ur.jsxs)("div",{style:{padding:"1rem",background:"#f8fafc",borderRadius:"8px"},children:[(0,ur.jsx)("h3",{children:"3. Optimization"}),(0,ur.jsx)("p",{children:"Get prioritized optimization tasks and strategies to improve your campaign performance."})]})]})]}),(0,ur.jsxs)("div",{className:"card",children:[(0,ur.jsx)("h2",{children:"Features"}),(0,ur.jsxs)("ul",{style:{listStyle:"none",padding:0},children:[(0,ur.jsxs)("li",{style:{padding:"0.5rem 0",borderBottom:"1px solid #e2e8f0"},children:["\u2705 ",(0,ur.jsx)("strong",{children:"CSV Data Processing:"})," Robust parsing and validation of advertising data"]}),(0,ur.jsxs)("li",{style:{padding:"0.5rem 0",borderBottom:"1px solid #e2e8f0"},children:["\u2705 ",(0,ur.jsx)("strong",{children:"AI-Powered Analysis:"})," LangChain agents for intelligent data analysis"]}),(0,ur.jsxs)("li",{style:{padding:"0.5rem 0",borderBottom:"1px solid #e2e8f0"},children:["\u2705 ",(0,ur.jsx)("strong",{children:"Performance Metrics:"})," ROAS, ACOS, CTR, and conversion rate calculations"]}),(0,ur.jsxs)("li",{style:{padding:"0.5rem 0",borderBottom:"1px solid #e2e8f0"},children:["\u2705 ",(0,ur.jsx)("strong",{children:"Keyword Optimization:"})," Top and bottom performer identification"]}),(0,ur.jsxs)("li",{style:{padding:"0.5rem 0",borderBottom:"1px solid #e2e8f0"},children:["\u2705 ",(0,ur.jsx)("strong",{children:"Actionable Insights:"})," Prioritized optimization recommendations"]}),(0,ur.jsxs)("li",{style:{padding:"0.5rem 0"},children:["\u2705 ",(0,ur.jsx)("strong",{children:"Real-time Processing:"})," Background job processing with status updates"]})]})]})]})};const dr=function(e){let{onJobCreated:t}=e;const[n,r]=(0,l.useState)(null),[a,o]=(0,l.useState)(!1),[i,s]=(0,l.useState)(0),[u,c]=(0,l.useState)(null),[d,f]=(0,l.useState)(null),[p,h]=(0,l.useState)(!1),m=(0,l.useRef)(null),v=te(),g=e=>{e&&"text/csv"===e.type?(r(e),f(null),c(null)):(f("Please select a valid CSV file"),r(null))};return(0,ur.jsxs)("div",{children:[(0,ur.jsxs)("div",{className:"card",children:[(0,ur.jsx)("h2",{children:"Upload CSV File"}),(0,ur.jsx)("p",{children:"Upload your Amazon PPC advertising data CSV file for analysis."}),(0,ur.jsxs)("div",{className:"upload-area ".concat(p?"dragover":""),onDragOver:e=>{e.preventDefault(),h(!0)},onDragLeave:e=>{e.preventDefault(),h(!1)},onDrop:e=>{e.preventDefault(),h(!1);const t=e.dataTransfer.files[0];g(t)},onClick:()=>{var e;return null===(e=m.current)||void 0===e?void 0:e.click()},children:[(0,ur.jsx)("input",{ref:m,type:"file",accept:".csv",onChange:e=>{const t=e.target.files[0];g(t)},className:"file-input"}),n?(0,ur.jsxs)("div",{children:[(0,ur.jsx)("div",{style:{fontSize:"2rem",marginBottom:"1rem"},children:"\ud83d\udcc4"}),(0,ur.jsx)("h3",{children:n.name}),(0,ur.jsxs)("p",{children:["Size: ",(e=>{if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]})(n.size)]}),(0,ur.jsxs)("p",{children:["Type: ",n.type]})]}):(0,ur.jsxs)("div",{children:[(0,ur.jsx)("div",{style:{fontSize:"3rem",marginBottom:"1rem"},children:"\ud83d\udcc1"}),(0,ur.jsx)("h3",{children:"Drop your CSV file here"}),(0,ur.jsx)("p",{children:"or click to browse files"}),(0,ur.jsx)("p",{style:{fontSize:"0.9rem",color:"#64748b",marginTop:"1rem"},children:"Supported format: CSV files up to 100MB"})]})]}),a&&(0,ur.jsxs)("div",{style:{margin:"1rem 0"},children:[(0,ur.jsx)("div",{className:"progress-bar",children:(0,ur.jsx)("div",{className:"progress-fill",style:{width:"".concat(i,"%")}})}),(0,ur.jsx)("p",{style:{textAlign:"center",margin:"0.5rem 0"},children:i<100?"Uploading... ".concat(i,"%"):"Processing..."})]}),u&&(0,ur.jsxs)("div",{className:"success"===u.type?"success":"error",children:[u.text,u.jobId&&(0,ur.jsxs)("div",{style:{marginTop:"0.5rem"},children:[(0,ur.jsx)("strong",{children:"Job ID:"})," ",u.jobId]})]}),d&&(0,ur.jsx)("div",{className:"error",children:d}),(0,ur.jsxs)("div",{style:{display:"flex",gap:"1rem",marginTop:"1rem"},children:[(0,ur.jsx)("button",{onClick:async()=>{if(!n)return void f("Please select a file first");o(!0),f(null),c(null);const e=new FormData;e.append("csvFile",n);try{const t=await sr.post("/api/upload/validate",e,{headers:{"Content-Type":"multipart/form-data"}});c({type:"success",text:"File is valid! Found ".concat(t.data.details.rowCount," rows. Estimated processing time: ").concat(t.data.details.estimatedProcessingTime,".")})}catch(i){var t,r,a,l;console.error("Validation error:",i),f((null===(t=i.response)||void 0===t||null===(r=t.data)||void 0===r?void 0:r.message)||(null===(a=i.response)||void 0===a||null===(l=a.data)||void 0===l?void 0:l.error)||"File validation failed.")}finally{o(!1)}},disabled:!n||a,className:"upload-button",style:{background:"#64748b"},children:a?"Validating...":"Validate File"}),(0,ur.jsx)("button",{onClick:async()=>{if(!n)return void f("Please select a file first");o(!0),s(0),f(null),c(null);const e=new FormData;e.append("csvFile",n);try{const n=await sr.post("/api/upload",e,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:e=>{const t=Math.round(100*e.loaded/e.total);s(t)}});c({type:"success",text:"File uploaded successfully! Processing started.",jobId:n.data.jobId}),t&&t(n.data.jobId),r(null),s(0),m.current&&(m.current.value=""),setTimeout((()=>{v("/results/".concat(n.data.jobId))}),2e3)}catch(d){var a,l,i,u;console.error("Upload error:",d),f((null===(a=d.response)||void 0===a||null===(l=a.data)||void 0===l?void 0:l.message)||(null===(i=d.response)||void 0===i||null===(u=i.data)||void 0===u?void 0:u.error)||"Upload failed. Please try again."),s(0)}finally{o(!1)}},disabled:!n||a,className:"upload-button",children:a?"Uploading...":"Upload & Process"})]})]}),(0,ur.jsxs)("div",{className:"card",children:[(0,ur.jsx)("h3",{children:"CSV File Requirements"}),(0,ur.jsx)("p",{children:"Your CSV file should contain the following columns:"}),(0,ur.jsxs)("ul",{style:{lineHeight:"1.8"},children:[(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"Matched product"})," - The product or keyword that was matched"]}),(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"Product targets"})," - Target products or keywords"]}),(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"Added as"})," - How the keyword was added (exact, phrase, broad)"]}),(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"Impressions"})," - Number of times the ad was shown"]}),(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"Clicks"})," - Number of clicks on the ad"]}),(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"CTR"})," - Click-through rate percentage"]}),(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"Spend(USD)"})," - Amount spent on advertising"]}),(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"CPC(USD)"})," - Cost per click"]}),(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"Orders"})," - Number of orders generated"]}),(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"Sales(USD)"})," - Total sales revenue"]}),(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"ACOS"})," - Advertising Cost of Sale percentage"]}),(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"ROAS"})," - Return on Ad Spend"]}),(0,ur.jsxs)("li",{children:[(0,ur.jsx)("strong",{children:"Conversion rate"})," - Percentage of clicks that converted"]})]}),(0,ur.jsxs)("div",{style:{marginTop:"1rem",padding:"1rem",background:"#f0f9ff",borderRadius:"8px",border:"1px solid #bae6fd"},children:[(0,ur.jsx)("h4",{style:{margin:"0 0 0.5rem 0",color:"#0369a1"},children:"\ud83d\udca1 Tips for best results:"}),(0,ur.jsxs)("ul",{style:{margin:0,paddingLeft:"1.2rem"},children:[(0,ur.jsx)("li",{children:"Ensure your CSV file has headers matching the required columns"}),(0,ur.jsx)("li",{children:"Remove any empty rows or columns"}),(0,ur.jsx)("li",{children:"Use standard number formats (no currency symbols in numeric fields)"}),(0,ur.jsx)("li",{children:"Maximum file size: 100MB"}),(0,ur.jsx)("li",{children:"Maximum rows: 50,000"})]})]})]})]})};const fr=function(e){let{jobId:t}=e;const{jobId:n}=function(){let{matches:e}=l.useContext(X),t=e[e.length-1];return t?t.params:{}}(),r=t||n,[a,o]=(0,l.useState)(null),[i,s]=(0,l.useState)(!0),[u,c]=(0,l.useState)(null),[d,f]=(0,l.useState)("overview"),[p,h]=(0,l.useState)(!1),[m,v]=(0,l.useState)(!1);(0,l.useEffect)((()=>{if(r){g();const e=setInterval(g,5e3);return()=>clearInterval(e)}}),[r]);const g=async()=>{try{const e=await sr.get("/api/analysis/".concat(r));o(e.data),c(null)}catch(n){var e,t;console.error("Error fetching job data:",n),c((null===(e=n.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to load job data")}finally{s(!1)}},y=e=>null===e||void 0===e?"N/A":"number"===typeof e?e.toFixed(2):e,b=e=>null===e||void 0===e?"N/A":"number"===typeof e?"$".concat(e.toFixed(2)):e;return r?i?(0,ur.jsx)("div",{className:"card",children:(0,ur.jsx)("div",{className:"loading",children:"Loading job data..."})}):u?(0,ur.jsxs)("div",{className:"card",children:[(0,ur.jsx)("div",{className:"error",children:u}),(0,ur.jsx)("button",{onClick:g,className:"upload-button",children:"Retry"})]}):(0,ur.jsxs)("div",{children:[(0,ur.jsxs)("div",{className:"card",children:[(0,ur.jsx)("h2",{children:"Job Status"}),(0,ur.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"1rem"},children:[(0,ur.jsxs)("div",{children:[(0,ur.jsx)("strong",{children:"File:"})," ",a.filename]}),(0,ur.jsxs)("div",{children:[(0,ur.jsx)("strong",{children:"Status:"})," ",(0,ur.jsx)("span",{className:(e=>{switch(e){case"pending":return"status-badge status-pending";case"processing":return"status-badge status-processing";case"completed":return"status-badge status-completed";case"failed":return"status-badge status-failed";default:return"status-badge"}})(a.status),children:a.status})]}),(0,ur.jsxs)("div",{children:[(0,ur.jsx)("strong",{children:"Progress:"})," ",a.progress||0,"%"]}),(0,ur.jsxs)("div",{children:[(0,ur.jsx)("strong",{children:"Created:"})," ",new Date(a.createdAt).toLocaleString()]})]}),void 0!==a.progress&&(0,ur.jsx)("div",{style:{margin:"1rem 0"},children:(0,ur.jsx)("div",{className:"progress-bar",children:(0,ur.jsx)("div",{className:"progress-fill",style:{width:"".concat(a.progress||0,"%")}})})}),"failed"===a.status&&a.error&&(0,ur.jsxs)("div",{className:"error",children:[(0,ur.jsx)("strong",{children:"Error:"})," ",a.error.message]})]}),"completed"===a.status&&(0,ur.jsxs)("div",{className:"card",children:[(0,ur.jsx)("h3",{children:"Available Actions"}),(0,ur.jsxs)("div",{style:{display:"flex",gap:"1rem",flexWrap:"wrap"},children:[(0,ur.jsx)("button",{onClick:async()=>{try{h(!0),await sr.post("/api/analysis/".concat(r,"/start"),{options:{includeInsights:!0,analysisDepth:"comprehensive",focusAreas:["keywords","performance","optimization"]}}),g()}catch(n){var e,t;console.error("Error starting analysis:",n),c((null===(e=n.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to start analysis"),h(!1)}},disabled:p,className:"upload-button",children:p?"Starting Analysis...":"Start AI Analysis"}),a.analysis&&(0,ur.jsx)("button",{onClick:async()=>{try{v(!0),await sr.post("/api/optimize/".concat(r),{priority:3,focusAreas:["keywords","bidding","targeting","budget"],includeKeywordExpansion:!0}),g()}catch(n){var e,t;console.error("Error starting optimization:",n),c((null===(e=n.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to start optimization"),v(!1)}},disabled:m,className:"upload-button",style:{background:"#059669"},children:m?"Generating Optimizations...":"Generate Optimizations"})]})]}),a.analysis&&(0,ur.jsxs)("div",{className:"card",children:[(0,ur.jsxs)("div",{className:"nav",style:{marginBottom:"2rem"},children:[(0,ur.jsx)("button",{onClick:()=>f("overview"),className:"nav-button ".concat("overview"===d?"active":""),children:"Overview"}),(0,ur.jsx)("button",{onClick:()=>f("insights"),className:"nav-button ".concat("insights"===d?"active":""),children:"Insights"}),(0,ur.jsx)("button",{onClick:()=>f("keywords"),className:"nav-button ".concat("keywords"===d?"active":""),children:"Keywords"}),(0,ur.jsx)("button",{onClick:()=>f("metrics"),className:"nav-button ".concat("metrics"===d?"active":""),children:"Metrics"})]}),"overview"===d&&a.analysis.aggregatedMetrics&&(0,ur.jsxs)("div",{children:[(0,ur.jsx)("h3",{children:"Campaign Overview"}),(0,ur.jsxs)("div",{className:"metrics-grid",children:[(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:y(a.analysis.aggregatedMetrics.total_rows)}),(0,ur.jsx)("div",{className:"metric-label",children:"Total Keywords"})]}),(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:y(a.analysis.aggregatedMetrics.total_impressions)}),(0,ur.jsx)("div",{className:"metric-label",children:"Total Impressions"})]}),(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:y(a.analysis.aggregatedMetrics.total_clicks)}),(0,ur.jsx)("div",{className:"metric-label",children:"Total Clicks"})]}),(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:b(a.analysis.aggregatedMetrics.total_spend)}),(0,ur.jsx)("div",{className:"metric-label",children:"Total Spend"})]}),(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:b(a.analysis.aggregatedMetrics.total_sales)}),(0,ur.jsx)("div",{className:"metric-label",children:"Total Sales"})]}),(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsx)("div",{className:"metric-value",children:y(a.analysis.aggregatedMetrics.calculated_roas)}),(0,ur.jsx)("div",{className:"metric-label",children:"ROAS"})]}),(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsxs)("div",{className:"metric-value",children:[y(a.analysis.aggregatedMetrics.calculated_acos),"%"]}),(0,ur.jsx)("div",{className:"metric-label",children:"ACOS"})]}),(0,ur.jsxs)("div",{className:"metric-card",children:[(0,ur.jsxs)("div",{className:"metric-value",children:[y(a.analysis.aggregatedMetrics.calculated_ctr),"%"]}),(0,ur.jsx)("div",{className:"metric-label",children:"CTR"})]})]})]}),"insights"===d&&(0,ur.jsxs)("div",{children:[(0,ur.jsx)("h3",{children:"AI-Generated Insights"}),a.analysis.insights&&a.analysis.insights.length>0?(0,ur.jsx)("ul",{className:"insights-list",children:a.analysis.insights.map(((e,t)=>(0,ur.jsxs)("li",{className:"insight-item",children:[(0,ur.jsx)("div",{className:"insight-title",children:e.title}),(0,ur.jsx)("div",{className:"insight-content",children:e.content}),(0,ur.jsxs)("div",{style:{marginTop:"0.5rem",fontSize:"0.875rem",color:"#64748b"},children:["Confidence: ",Math.round(100*(e.confidence_score||0)),"%"]})]},t)))}):(0,ur.jsx)("p",{children:"No insights available. Start AI analysis to generate insights."})]}),"keywords"===d&&(0,ur.jsxs)("div",{children:[(0,ur.jsx)("h3",{children:"Keyword Performance"}),a.analysis.topPerformers&&a.analysis.topPerformers.length>0&&(0,ur.jsxs)("div",{style:{marginBottom:"2rem"},children:[(0,ur.jsx)("h4",{children:"Top Performing Keywords"}),(0,ur.jsx)("div",{className:"table-container",children:(0,ur.jsxs)("table",{className:"table",children:[(0,ur.jsx)("thead",{children:(0,ur.jsxs)("tr",{children:[(0,ur.jsx)("th",{children:"Keyword"}),(0,ur.jsx)("th",{children:"Impressions"}),(0,ur.jsx)("th",{children:"Clicks"}),(0,ur.jsx)("th",{children:"Spend"}),(0,ur.jsx)("th",{children:"Sales"}),(0,ur.jsx)("th",{children:"ROAS"})]})}),(0,ur.jsx)("tbody",{children:a.analysis.topPerformers.slice(0,10).map(((e,t)=>(0,ur.jsxs)("tr",{children:[(0,ur.jsx)("td",{children:e.matched_product}),(0,ur.jsx)("td",{children:y(e.total_impressions)}),(0,ur.jsx)("td",{children:y(e.total_clicks)}),(0,ur.jsx)("td",{children:b(e.total_spend)}),(0,ur.jsx)("td",{children:b(e.total_sales)}),(0,ur.jsx)("td",{children:y(e.avg_roas)})]},t)))})]})})]}),a.analysis.bottomPerformers&&a.analysis.bottomPerformers.length>0&&(0,ur.jsxs)("div",{children:[(0,ur.jsx)("h4",{children:"Underperforming Keywords"}),(0,ur.jsx)("div",{className:"table-container",children:(0,ur.jsxs)("table",{className:"table",children:[(0,ur.jsx)("thead",{children:(0,ur.jsxs)("tr",{children:[(0,ur.jsx)("th",{children:"Keyword"}),(0,ur.jsx)("th",{children:"Impressions"}),(0,ur.jsx)("th",{children:"Clicks"}),(0,ur.jsx)("th",{children:"Spend"}),(0,ur.jsx)("th",{children:"Sales"}),(0,ur.jsx)("th",{children:"ROAS"})]})}),(0,ur.jsx)("tbody",{children:a.analysis.bottomPerformers.slice(0,10).map(((e,t)=>(0,ur.jsxs)("tr",{children:[(0,ur.jsx)("td",{children:e.matched_product}),(0,ur.jsx)("td",{children:y(e.total_impressions)}),(0,ur.jsx)("td",{children:y(e.total_clicks)}),(0,ur.jsx)("td",{children:b(e.total_spend)}),(0,ur.jsx)("td",{children:b(e.total_sales)}),(0,ur.jsx)("td",{children:y(e.avg_roas)})]},t)))})]})})]})]}),"metrics"===d&&(0,ur.jsxs)("div",{children:[(0,ur.jsx)("h3",{children:"Detailed Metrics"}),a.analysis.metrics&&a.analysis.metrics.length>0?(0,ur.jsx)("div",{className:"table-container",children:(0,ur.jsxs)("table",{className:"table",children:[(0,ur.jsx)("thead",{children:(0,ur.jsxs)("tr",{children:[(0,ur.jsx)("th",{children:"Metric"}),(0,ur.jsx)("th",{children:"Value"}),(0,ur.jsx)("th",{children:"Calculated At"})]})}),(0,ur.jsx)("tbody",{children:a.analysis.metrics.map(((e,t)=>(0,ur.jsxs)("tr",{children:[(0,ur.jsx)("td",{children:e.metric_type.replace(/_/g," ").toUpperCase()}),(0,ur.jsx)("td",{children:y(e.metric_value)}),(0,ur.jsx)("td",{children:new Date(e.created_at).toLocaleString()})]},t)))})]})}):(0,ur.jsx)("p",{children:"No detailed metrics available."})]})]})]}):(0,ur.jsxs)("div",{className:"card",children:[(0,ur.jsx)("h2",{children:"No Job Selected"}),(0,ur.jsx)("p",{children:"Please upload a file first or select a job from the dashboard."}),(0,ur.jsx)("a",{href:"/upload",className:"upload-button",children:"Upload File"})]})};function pr(){const e=Z();return(0,ur.jsxs)("nav",{className:"nav",children:[(0,ur.jsx)(Pe,{to:"/",className:"nav-button ".concat("/"===e.pathname?"active":""),children:"Dashboard"}),(0,ur.jsx)(Pe,{to:"/upload",className:"nav-button ".concat("/upload"===e.pathname?"active":""),children:"Upload Data"}),(0,ur.jsx)(Pe,{to:"/results",className:"nav-button ".concat("/results"===e.pathname?"active":""),children:"View Results"})]})}const hr=function(){const[e,t]=(0,l.useState)(null);return(0,ur.jsx)(Ce,{children:(0,ur.jsxs)("div",{className:"App",children:[(0,ur.jsx)("header",{className:"header",children:(0,ur.jsxs)("div",{className:"container",children:[(0,ur.jsx)("h1",{children:"Ad Data Analysis Platform"}),(0,ur.jsx)("p",{children:"AI-powered advertising campaign optimization and insights"})]})}),(0,ur.jsxs)("div",{className:"container",children:[(0,ur.jsx)(pr,{}),(0,ur.jsxs)(ye,{children:[(0,ur.jsx)(ve,{path:"/",element:(0,ur.jsx)(cr,{})}),(0,ur.jsx)(ve,{path:"/upload",element:(0,ur.jsx)(dr,{onJobCreated:t})}),(0,ur.jsx)(ve,{path:"/results",element:(0,ur.jsx)(fr,{jobId:e})}),(0,ur.jsx)(ve,{path:"/results/:jobId",element:(0,ur.jsx)(fr,{})})]})]})]})})};i.createRoot(document.getElementById("root")).render((0,ur.jsx)(l.StrictMode,{children:(0,ur.jsx)(hr,{})}))})();
//# sourceMappingURL=main.115b848b.js.map