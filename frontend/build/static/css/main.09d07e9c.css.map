{"version": 3, "file": "static/css/main.09d07e9c.css", "mappings": "AAAA,EACE,qBACF,CAEA,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CACzB,aAAc,CANd,mIAEY,CAHZ,QAQF,CAEA,WAEE,aAAc,CADd,gBAAiB,CAEjB,YACF,CAEA,QACE,kDAA6D,CAI7D,8BAAwC,CAHxC,UAAY,CAEZ,kBAAmB,CADnB,cAGF,CAEA,WAGE,gBAAiB,CACjB,eAAgB,CAHhB,QAAS,CACT,iBAGF,CAEA,UAIE,gBAAiB,CAFjB,gBAAoB,CACpB,UAAY,CAFZ,iBAIF,CAEA,KACE,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,aACF,CAEA,YAEE,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAMlB,aAAc,CALd,cAAe,CACf,cAAe,CACf,eAAgB,CANhB,qBAAuB,CAOvB,kBAGF,CAEA,kBACE,oBAAqB,CACrB,aAAc,CACd,0BACF,CAEA,mBACE,kBAAmB,CAEnB,oBAAqB,CADrB,UAEF,CAEA,MACE,eAAiB,CACjB,kBAAmB,CAEnB,8BAAwC,CACxC,kBAAmB,CAFnB,YAGF,CAEA,aACE,yBAA0B,CAC1B,kBAAmB,CAInB,cAAe,CAHf,YAAa,CACb,iBAAkB,CAClB,kBAEF,CAEA,mBAEE,wBAAyB,CADzB,oBAEF,CAEA,sBAEE,wBAAyB,CADzB,oBAEF,CAEA,YACE,YACF,CAEA,eACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAMZ,cAAe,CAFf,cAAe,CACf,eAAgB,CAGhB,eAAgB,CANhB,mBAAqB,CAKrB,kBAEF,CAEA,qBACE,kBAAmB,CACnB,0BACF,CAEA,wBACE,kBAAmB,CACnB,kBAAmB,CACnB,cACF,CAEA,cAGE,oBAAqB,CAFrB,oBAAqB,CAGrB,iBAAmB,CACnB,eAAgB,CAHhB,qBAAwB,CAIxB,wBACF,CAEA,gBACE,kBAAmB,CACnB,aACF,CAEA,mBACE,kBAAmB,CACnB,aACF,CAEA,kBACE,kBAAmB,CACnB,aACF,CAEA,eACE,kBAAmB,CACnB,aACF,CAEA,cAGE,kBAAmB,CACnB,iBAAkB,CAFlB,UAAW,CAIX,aAAc,CADd,eAAgB,CAJhB,UAMF,CAEA,eAEE,kBAAmB,CADnB,WAAY,CAEZ,yBACF,CAEA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,aACF,CAEA,aACE,eAAiB,CAEjB,iBAAkB,CAClB,8BAAwC,CAFxC,cAAe,CAGf,iBACF,CAEA,cAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,mBACF,CAEA,cAEE,aAAc,CADd,iBAAmB,CAGnB,eAAgB,CADhB,wBAEF,CAEA,eACE,eAAgB,CAChB,SACF,CAEA,cACE,kBAAmB,CAInB,6BAA8B,CAF9B,iBAAkB,CAClB,kBAAmB,CAFnB,YAIF,CAEA,eAGE,aAAc,CAFd,eAAgB,CAChB,mBAEF,CAEA,iBACE,aAAc,CACd,eACF,CAEA,SAGE,kBAAmB,CAGnB,aAAc,CALd,YAAa,CAIb,gBAAiB,CAHjB,sBAAuB,CAEvB,YAGF,CAEA,OACE,kBAAmB,CAKnB,wBAAyB,CAFzB,iBAAkB,CAFlB,aAKF,CAEA,gBAJE,aAAc,CAFd,YAaF,CAPA,SACE,kBAAmB,CAKnB,wBAAyB,CAFzB,iBAAkB,CAFlB,aAKF,CAEA,OAEE,wBAAyB,CACzB,aAAc,CAFd,UAGF,CAEA,oBAIE,+BAAgC,CAFhC,cAAgB,CAChB,eAEF,CAEA,UAGE,aAAc,CADd,eAEF,CAEA,0BALE,kBAOF,CAEA,yBACE,WACE,YACF,CAEA,WACE,cACF,CAEA,KAEE,kBAAmB,CADnB,qBAEF,CAEA,cACE,yBACF,CAEA,MACE,YACF,CACF,CCxSA,KACE,gBACF,CAEA,YACE,oBAEF,CAEA,8BAHE,oBAKF,CAGA,iBACE,GACE,SACF,CACA,IACE,UACF,CACA,GACE,SACF,CACF,CAEA,SACE,2BACF,CAGA,yBACE,KACE,cAAe,CACf,SACF,CAEA,YACE,QAAO,CACP,eAAgB,CAChB,iBACF,CACF", "sources": ["index.css", "App.css"], "sourcesContent": ["* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f8fafc;\n  color: #334155;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 2rem 0;\n  margin-bottom: 2rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n\n.header h1 {\n  margin: 0;\n  text-align: center;\n  font-size: 2.5rem;\n  font-weight: 700;\n}\n\n.header p {\n  text-align: center;\n  margin: 0.5rem 0 0 0;\n  opacity: 0.9;\n  font-size: 1.1rem;\n}\n\n.nav {\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n  margin: 2rem 0;\n}\n\n.nav-button {\n  padding: 0.75rem 1.5rem;\n  background: white;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 500;\n  transition: all 0.2s;\n  text-decoration: none;\n  color: #475569;\n}\n\n.nav-button:hover {\n  border-color: #667eea;\n  color: #667eea;\n  transform: translateY(-1px);\n}\n\n.nav-button.active {\n  background: #667eea;\n  color: white;\n  border-color: #667eea;\n}\n\n.card {\n  background: white;\n  border-radius: 12px;\n  padding: 2rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  margin-bottom: 2rem;\n}\n\n.upload-area {\n  border: 2px dashed #cbd5e1;\n  border-radius: 12px;\n  padding: 3rem;\n  text-align: center;\n  transition: all 0.2s;\n  cursor: pointer;\n}\n\n.upload-area:hover {\n  border-color: #667eea;\n  background-color: #f8fafc;\n}\n\n.upload-area.dragover {\n  border-color: #667eea;\n  background-color: #eff6ff;\n}\n\n.file-input {\n  display: none;\n}\n\n.upload-button {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 0.75rem 2rem;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  margin-top: 1rem;\n}\n\n.upload-button:hover {\n  background: #5a67d8;\n  transform: translateY(-1px);\n}\n\n.upload-button:disabled {\n  background: #94a3b8;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.status-badge {\n  display: inline-block;\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  text-transform: uppercase;\n}\n\n.status-pending {\n  background: #fef3c7;\n  color: #92400e;\n}\n\n.status-processing {\n  background: #dbeafe;\n  color: #1e40af;\n}\n\n.status-completed {\n  background: #d1fae5;\n  color: #065f46;\n}\n\n.status-failed {\n  background: #fee2e2;\n  color: #991b1b;\n}\n\n.progress-bar {\n  width: 100%;\n  height: 8px;\n  background: #e2e8f0;\n  border-radius: 4px;\n  overflow: hidden;\n  margin: 1rem 0;\n}\n\n.progress-fill {\n  height: 100%;\n  background: #667eea;\n  transition: width 0.3s ease;\n}\n\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin: 2rem 0;\n}\n\n.metric-card {\n  background: white;\n  padding: 1.5rem;\n  border-radius: 8px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  text-align: center;\n}\n\n.metric-value {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #667eea;\n  margin-bottom: 0.5rem;\n}\n\n.metric-label {\n  font-size: 0.875rem;\n  color: #64748b;\n  text-transform: uppercase;\n  font-weight: 500;\n}\n\n.insights-list {\n  list-style: none;\n  padding: 0;\n}\n\n.insight-item {\n  background: #f8fafc;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n  border-left: 4px solid #667eea;\n}\n\n.insight-title {\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: #1e293b;\n}\n\n.insight-content {\n  color: #475569;\n  line-height: 1.6;\n}\n\n.loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 3rem;\n  font-size: 1.1rem;\n  color: #64748b;\n}\n\n.error {\n  background: #fee2e2;\n  color: #991b1b;\n  padding: 1rem;\n  border-radius: 8px;\n  margin: 1rem 0;\n  border: 1px solid #fecaca;\n}\n\n.success {\n  background: #d1fae5;\n  color: #065f46;\n  padding: 1rem;\n  border-radius: 8px;\n  margin: 1rem 0;\n  border: 1px solid #a7f3d0;\n}\n\n.table {\n  width: 100%;\n  border-collapse: collapse;\n  margin: 1rem 0;\n}\n\n.table th,\n.table td {\n  padding: 0.75rem;\n  text-align: left;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.table th {\n  background: #f8fafc;\n  font-weight: 600;\n  color: #374151;\n}\n\n.table tr:hover {\n  background: #f8fafc;\n}\n\n@media (max-width: 768px) {\n  .container {\n    padding: 10px;\n  }\n  \n  .header h1 {\n    font-size: 2rem;\n  }\n  \n  .nav {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .card {\n    padding: 1rem;\n  }\n}\n", "/* Additional styles specific to App component */\n\n.App {\n  min-height: 100vh;\n}\n\n.nav-button {\n  display: inline-block;\n  text-decoration: none;\n}\n\n.nav-button:hover {\n  text-decoration: none;\n}\n\n/* Animation for loading states */\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n.loading {\n  animation: pulse 2s infinite;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .nav {\n    flex-wrap: wrap;\n    gap: 0.5rem;\n  }\n  \n  .nav-button {\n    flex: 1;\n    min-width: 120px;\n    text-align: center;\n  }\n}\n"], "names": [], "sourceRoot": ""}