[{"/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/frontend/src/index.js": "1", "/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/frontend/src/App.js": "2", "/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/frontend/src/components/Upload.js": "3", "/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/frontend/src/components/Dashboard.js": "4", "/home/<USER>/Documents/Harsh Gohil/Project/AakaarAI/frontend/src/components/Results.js": "5"}, {"size": 254, "mtime": 1749996994590, "results": "6", "hashOfConfig": "7"}, {"size": 1851, "mtime": 1749997036460, "results": "8", "hashOfConfig": "7"}, {"size": 8755, "mtime": 1749997114265, "results": "9", "hashOfConfig": "7"}, {"size": 8483, "mtime": 1749997079317, "results": "10", "hashOfConfig": "7"}, {"size": 14367, "mtime": 1749997161589, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4dqa6v", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/Documents/Harsh <PERSON>/Project/AakaarAI/frontend/src/index.js", [], [], "/home/<USER>/Documents/Harsh <PERSON>/Project/AakaarAI/frontend/src/App.js", [], [], "/home/<USER>/Documents/Harsh <PERSON>/Project/AakaarAI/frontend/src/components/Upload.js", [], [], "/home/<USER>/Documents/Harsh <PERSON>/Project/AakaarAI/frontend/src/components/Dashboard.js", [], [], "/home/<USER>/Documents/Harsh <PERSON>/Project/AakaarAI/frontend/src/components/Results.js", ["27"], [], {"ruleId": "28", "severity": 1, "message": "29", "line": 22, "column": 6, "nodeType": "30", "endLine": 22, "endColumn": 13, "suggestions": "31"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchJobData'. Either include it or remove the dependency array.", "ArrayExpression", ["32"], {"desc": "33", "fix": "34"}, "Update the dependencies array to be: [fetchJobData, jobId]", {"range": "35", "text": "36"}, [787, 794], "[fetch<PERSON><PERSON><PERSON><PERSON>, jobId]"]